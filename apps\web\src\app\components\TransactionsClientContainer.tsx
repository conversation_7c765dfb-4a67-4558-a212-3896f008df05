"use client";
import {
  Badge,
  FetchDocuments,
  FilterBadges,
  generateFilterBadgesFromFilters,
  Input,
  TransactionActionFunction,
  TransactionCategory,
  TransactionsContainer,
  Category,
  Member,
  Group,
  getListFromParams,
} from "@repo/ui";
import { useState, useCallback, useEffect, useMemo } from "react";
import { firebaseStorage } from "../lib/firebase/config";
import { ref, uploadBytes } from "firebase/storage";
import { ExcludedCashFlowV2, GroupIcon } from "@repo/ui/icons";
import { TransactionGroup } from "@repo/ui";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { format, parseISO } from "date-fns";
import { usePagination } from "@repo/ui";

type TransactionsClientContainer = {
  initialTransactions: TransactionGroup[];
  initialPage: number;
  fetchMoreTransactions: (page: number) => Promise<TransactionGroup[]>;
  changeCategoryAction: (
    transactionId: string,
    categoryId: string,
    subCategoryId: string,
  ) => Promise<void>;
  updateTransaction: TransactionActionFunction;
  fetchTransactionDocuments: (transactionId: string) => Promise<FetchDocuments>;
  deleteTransactionDocuments: (
    transactionId: string,
    fileNames: string[],
  ) => Promise<{ ok: boolean }>;
  uploadFile?: (transactionId: string, file: File) => void;
  authToken?: string;
  phoneNumber: string;
  filters: {
    fromDate?: string;
    toDate?: string;
    minAmount?: string;
    maxAmount?: string;
    transactionType?: string;
    tagStatus?: string;
    bookmarkOptions?: {
      showFavorites?: boolean;
      excludedFromCashflow?: boolean;
      withNotes?: boolean;
    };
    categoryFilters?: TransactionCategory[];
  };
  members: Member[];
  groups: Group[];
  filter?: {
    userGroups?: {
      userIds?: Array<{ id: string }>;
      userGroupIds?: Array<{ id: string }>;
    };
    accountFilters?: {
      accountIds?: Array<{ id: string }>;
    };
    txnFilters?: {
      timeRange?: {
        fromTime?: string;
        toTime?: string;
      };
      amountRange?: {
        minAmount?: string;
        maxAmount?: string;
      };
      favorited?: {
        favorited: boolean;
      };
      excludeCashFlow?: {
        excludeCashFlow: boolean;
      };
      hasUserNotes?: {
        hasUserNotes: boolean;
      };
      untagged?: {
        untagged: boolean;
      };
      txnType?: {
        txnType: string;
      };
      category?: TransactionCategory[];
    };
  };
};

const defaultFilters = {
  selectedRange: undefined,
  selectedAmountRange: [0, 200000],
  selectedDateLabel: "All time",
  selectedMembers: [],
  selectedGroups: [],
  selectedAccounts: {accountIds: new Set<string>()},
  selectedBookmarkOptions: {
    showFavorites: false,
    excludedFromCashflow: false,
    withNotes: false,
  },
  selectedTags: [],
  selectedTransactionType: "All",
  selectedTagStatus: "All",
  selectedCategories: {},
  categoryFilters: [],
};

function TransactionsClientContainer({
  initialTransactions,
  initialPage,
  fetchMoreTransactions,
  changeCategoryAction,
  updateTransaction,
  fetchTransactionDocuments,
  deleteTransactionDocuments,
  phoneNumber,
  filters,
  filter,
  authToken,
  members,
  groups,
}: TransactionsClientContainer) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [clearFilterFunction, setClearFilterFunction] = useState<
    ((value: string | undefined) => void) | null
  >(null);
  const [categoriesData, setCategoriesData] = useState<Category[]>([]);
  const [membersState, setMembersState] = useState<Member[]>(members);
  const [groupsState, setGroupsState] = useState<Group[]>(groups);

  const {
    data: transactions,
    lastItemRef,
    setPage,
    setHasMore,
    hasMore,
    resetPagination,
  } = usePagination({
    initialData: initialTransactions,
    initialPage,
    fetchMore: fetchMoreTransactions,
  });

  const handleCategoriesData = useCallback((categories: Category[]) => {
    setCategoriesData(categories);
  }, []);

  const handleUploadFile = (transactionId: string, file: File) => {
    const path = `${phoneNumber}/${transactionId}/${file?.name}`;
    const storageRef = ref(firebaseStorage, path);

    uploadBytes(storageRef, file).then((snapshot) => {
      console.log("File Uploaded Successfully!!!");
    });
  };

  useEffect(() => {
    async function fetchData() {
      if (!authToken) {
        return;
      }
      try {
        const response = await fetch(
          `/api/memberAndGroup?authToken=${encodeURIComponent(authToken)}`,
        );
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `Error fetching data: ${response.status} - ${errorText}`,
          );
        }
        const data = await response.json();
        if (data.members?.length > 0) {
          setMembersState(data.members);
        }
        if (data.groups?.length > 0) {
          setGroupsState(data.groups);
        }
      } catch (error) {
        console.error("Error fetching members and groups:", error);
      }
    }

    fetchData();
  }, [authToken]);

  const getInitialFilters = useCallback(() => {
    const formatDate = (dateString: string) =>
      format(parseISO(dateString), "MMM d");
    const fromDateParam = searchParams?.get("fromDate");
    const toDateParam = searchParams?.get("toDate");
    let dateRange = undefined;
    let dateLabel = "All time";
    if (fromDateParam && toDateParam) {
      const from = parseISO(fromDateParam);
      const to = parseISO(toDateParam);
      dateRange = { from, to };
      dateLabel = `${formatDate(fromDateParam)} - ${formatDate(toDateParam)}, ${format(parseISO(toDateParam), "yyyy")}`;
    }
    const minAmount = searchParams?.get("minAmount")
      ? parseInt(searchParams.get("minAmount")!)
      : 0;
    const maxAmount = searchParams?.get("maxAmount")
      ? parseInt(searchParams.get("maxAmount")!)
      : 200000;
    const transactionType = searchParams?.get("transactionType") || "All";

    const showFavorites = searchParams?.get("showFavorites") === "true";
    const excludedFromCashflow =
      searchParams?.get("excludedFromCashflow") === "true";
    const withNotes = searchParams?.get("withNotes") === "true";
    const tagStatus = searchParams?.get("tagStatus") || "All";
    let selectedMembers: string[] = [];
    let selectedGroups: string[] = [];
    if (searchParams !== null) {
      selectedMembers = getListFromParams(
        searchParams,
        "memberCount",
        "memberId",
      );
      selectedGroups = getListFromParams(searchParams, "groupCount", "groupId");
    }

    const updatedFilters = {
      ...defaultFilters,
      selectedRange: dateRange,
      selectedDateLabel: dateLabel,
      selectedAmountRange: [minAmount, maxAmount],
      selectedTransactionType: transactionType,
      selectedTagStatus: tagStatus,
      selectedBookmarkOptions: {
        showFavorites,
        excludedFromCashflow,
        withNotes,
      },
      categoryFilters: [] as TransactionCategory[],
      selectedCategories: {} as { [categoryId: string]: string[] },
      selectedMembers,
      selectedGroups,
      selectedAccounts: { accountIds: new Set<string>() },
    };
    const categoryFiltersCount = searchParams?.get("categoryFiltersCount");
    if (categoryFiltersCount) {
      const count = parseInt(categoryFiltersCount);
      const categoryFilters = Array.from({ length: count }, (_, i) => {
        const categoryId = searchParams?.get(`categoryId_${i}`);
        const subcategoryId = searchParams?.get(`subcategoryId_${i}`);

        return categoryId
          ? {
              categoryCollection: "",
              categoryId: categoryId,
              subcategoryId: subcategoryId || "",
            }
          : null;
      }).filter(Boolean) as TransactionCategory[];

      const selectedCategories = categoryFilters.reduce(
        (acc, filter) => {
          if (filter.categoryId) {
            if (!acc[filter.categoryId]) {
              acc[filter.categoryId] = [];
            }
            if (filter.subcategoryId) {
              acc[filter.categoryId]!.push(filter.subcategoryId);
            }
          }
          return acc;
        },
        {} as { [categoryId: string]: string[] },
      );

      updatedFilters.categoryFilters = categoryFilters;
      updatedFilters.selectedCategories = selectedCategories;
    }
    if (filter) {
      // Handle camelCase properties
      if (filter?.userGroups) {
        if (
          filter?.userGroups.userIds &&
          filter?.userGroups.userIds.length > 0
        ) {
          updatedFilters.selectedMembers = filter.userGroups.userIds.map(
            (item: { id: string }) => item.id,
          );
        }

        if (
          filter.userGroups.userGroupIds &&
          filter.userGroups.userGroupIds.length > 0
        ) {
          updatedFilters.selectedGroups = filter.userGroups.userGroupIds.map(
            (item: { id: string }) => item.id,
          );
        }
      }

      if (filter.accountFilters && filter.accountFilters?.accountIds) {
        const accountIds = new Set<string>();
        filter.accountFilters.accountIds.forEach((account: { id: string }) => {
          accountIds.add(account.id);
        });
        updatedFilters.selectedAccounts = { accountIds };
      }

      if (filter.txnFilters?.timeRange) {
        const timeRange = filter.txnFilters.timeRange;
        if (timeRange.fromTime && timeRange.toTime) {
          const from = new Date(timeRange.fromTime);
          const to = new Date(timeRange.toTime);
          updatedFilters.selectedRange = { from, to };
          updatedFilters.selectedDateLabel = `${formatDate(timeRange.fromTime)} - ${formatDate(timeRange.toTime)}, ${format(new Date(timeRange.toTime), "yyyy")}`;
        }
      }

      if (filter.txnFilters?.amountRange) {
        const amountRange = filter.txnFilters.amountRange;
        if (
          amountRange.minAmount !== undefined &&
          amountRange.maxAmount !== undefined
        ) {
          updatedFilters.selectedAmountRange = [
            parseInt(amountRange.minAmount),
            parseInt(amountRange.maxAmount),
          ];
        }
      }

      if (filter?.txnFilters?.favorited) {
        updatedFilters.selectedBookmarkOptions.showFavorites =
          filter?.txnFilters?.favorited.favorited;
      }

      if (filter?.txnFilters?.excludeCashFlow) {
        updatedFilters.selectedBookmarkOptions.excludedFromCashflow =
          filter?.txnFilters?.excludeCashFlow.excludeCashFlow;
      }

      if (filter?.txnFilters?.hasUserNotes?.hasUserNotes) {
        updatedFilters.selectedBookmarkOptions.withNotes =
          !!filter?.txnFilters?.hasUserNotes?.hasUserNotes;
      }

      if (filter?.txnFilters?.txnType) {
        updatedFilters.selectedTransactionType =
          filter?.txnFilters?.txnType.txnType === "CREDIT"
            ? "Incoming"
            : "Outgoing";
      }

      if (filter?.txnFilters?.untagged !== undefined) {
        updatedFilters.selectedTagStatus = filter?.txnFilters?.untagged
          .untagged
          ? "Untagged"
          : "Tagged";
      }
    }

    return updatedFilters;
  }, [searchParams, filters, filter]);

  const [initialFilter] = useState(() => getInitialFilters());
  const [currentFilters, setCurrentFilters] = useState(initialFilter);
  const [filterBadges, setFilterBadges] = useState<string[]>([]);

  //updating badges
  useEffect(() => {
    if (categoriesData.length === 0) return;
    setFilterBadges(
      generateFilterBadgesFromFilters(
        currentFilters,
        categoriesData,
        membersState,
        groupsState,
      ),
    );
  }, [currentFilters, categoriesData, membersState, groupsState]);

  // Sync initialFilters with currentFilters
  useEffect(() => {
    setCurrentFilters(initialFilter);
  }, [initialFilter]);

  useEffect(() => {
    resetPagination();
  }, [initialTransactions]);

  const appliedCount = useMemo(() => {
    let count = 0;
    if (
      currentFilters.selectedDateLabel !== "All time" &&
      currentFilters.selectedRange !== undefined
    ) {
      count++;
    }
    if (
      currentFilters.selectedAmountRange[0]! > 0 ||
      currentFilters.selectedAmountRange[1]! < 200000
    ) {
      count++;
    }
    if (
      currentFilters.selectedMembers.length > 0 ||
      currentFilters.selectedGroups.length > 0 ||
      currentFilters.selectedTags.length > 0
    ) {
      count++;
    }

    const bookmarks = currentFilters.selectedBookmarkOptions;
    if (bookmarks.showFavorites) {
      count++;
    }
    if (bookmarks.withNotes) {
      count++;
    }
    if (bookmarks.excludedFromCashflow) {
      count++;
    }
    if (currentFilters.selectedTagStatus !== "All") {
      count++;
    }
    if (currentFilters.selectedTransactionType !== "All") {
      count++;
    }

    if (Object.keys(currentFilters.selectedCategories).length > 0) {
      count++;
    }

    return count;
  }, [currentFilters]);

  const handleClearAllFilters = useCallback(() => {
    setCurrentFilters(defaultFilters);
    setFilterBadges([]);
    setHasMore(true);
    setPage(initialPage);

    if(pathname !== null){
      router.push(pathname);
    }
  }, [router, pathname]);

  const handleFilterStateChange = useCallback((isOpen: boolean) => {
    setIsFilterOpen(isOpen);
  }, []);

  const handleFilterBadgesChange = useCallback(
    (badges: string[]) => {
      setFilterBadges(badges);

      const updatedFilters = getInitialFilters();
      setCurrentFilters(updatedFilters);
      setHasMore(true);
    },
    [getInitialFilters],
  );

  const handleApplyAllFilters = useCallback(
    async (filters: any) => {
      setCurrentFilters(filters);

      const searchParams = new URLSearchParams();
      if (filters.selectedMembers.length > 0) {
        searchParams.set(
          "memberCount",
          filters.selectedMembers.length.toString(),
        );
        Array.from({ length: filters.selectedMembers.length }, (_, i) => {
          searchParams.set(`memberId_${i}`, filters.selectedMembers[i]);
        });
        searchParams.set("memberMatch", "true");
      } else {
        searchParams.delete("memberCount");
        searchParams.delete("memberMatch");
      }
      if (filters.selectedGroups.length > 0) {
        searchParams.set(
          "groupCount",
          filters.selectedGroups.length.toString(),
        );
        Array.from({ length: filters.selectedGroups.length }, (_, i) => {
          searchParams.set(`groupId_${i}`, filters.selectedGroups[i]);
        });
        searchParams.set("groupMatch", "true");
      } else {
        searchParams.delete("groupCount");
        searchParams.delete("groupMatch");
      }
      const selectedAccountIds = [
        ...(filters.selectedAccounts?.accountIds || []),
      ];
      if (selectedAccountIds.length > 0) {
        searchParams.set("accountCount", selectedAccountIds.length.toString());
        Array.from({ length: selectedAccountIds.length }, (_, i) => {
          searchParams.set(`accountId_${i}`, selectedAccountIds[i]!);
        });
        searchParams.set("accountMatch", "true");
      } else {
        searchParams.delete("accountCount");
        searchParams.delete("accountMatch");
        const currentParams = new URLSearchParams(window.location.search);
        const accountCount = parseInt(currentParams.get("accountCount") || "0");
        Array.from({ length: accountCount }, (_, i) => {
          searchParams.delete(`accountId_${i}`);
        });
      }
      if (filters.selectedRange?.from && filters.selectedRange?.to) {
        const fromDateStr = format(filters.selectedRange.from, "yyyy-MM-dd");
        const toDateStr = format(filters.selectedRange.to, "yyyy-MM-dd");
        searchParams.set("fromDate", fromDateStr);
        searchParams.set("toDate", toDateStr);
      }

      if (
        filters.selectedAmountRange &&
        (filters.selectedAmountRange[0] > 0 ||
          filters.selectedAmountRange[1] < 200000)
      ) {
        searchParams.set(
          "minAmount",
          filters.selectedAmountRange[0].toString(),
        );
        searchParams.set(
          "maxAmount",
          filters.selectedAmountRange[1].toString(),
        );
      }
      if (
        filters.selectedTransactionType &&
        filters.selectedTransactionType !== "All"
      ) {
        searchParams.set("transactionType", filters.selectedTransactionType);
      }
      if (filters.selectedBookmarkOptions.showFavorites) {
        searchParams.set("showFavorites", "true");
      }
      if (filters.selectedTagStatus && filters.selectedTagStatus !== "All") {
        searchParams.set("tagStatus", filters.selectedTagStatus);
      }
      if (filters.selectedBookmarkOptions.excludedFromCashflow) {
        searchParams.set("excludedFromCashflow", "true");
      }

      if (filters.selectedBookmarkOptions.withNotes) {
        searchParams.set("withNotes", "true");
      }
      if (filters.categoryFilters && filters.categoryFilters.length > 0) {
        searchParams.set(
          "categoryFiltersCount",
          filters.categoryFilters.length.toString(),
        );

        Array.from({ length: filters.categoryFilters.length }, (_, i) => {
          const filter = filters.categoryFilters[i];
          if ('category_id' in filter && filter.category_id) {
            searchParams.set(`categoryId_${i}`, filter.category_id);
            searchParams.set(`subcategoryId_${i}`, filter.subcategory_id || "");
          } else if ('categoryId' in filter && filter.categoryId) {
            searchParams.set(`categoryId_${i}`, filter.categoryId);
            searchParams.set(`subcategoryId_${i}`, filter.subcategoryId || "");
          }
        });
      } else {
        searchParams.delete("categoryFiltersCount");
        const currentParams = new URLSearchParams(window.location.search);
        const categoryCount = parseInt(
          currentParams.get("categoryFiltersCount") || "0",
        );
        Array.from({ length: categoryCount }, (_, i) => {
          searchParams.delete(`categoryId_${i}`);
          searchParams.delete(`subcategoryId_${i}`);
        });
      }
      const query = searchParams.toString();
      router.push(query ? `?${query}` : "/dashboard");
      setHasMore(true);
    },
    [router],
  );

  const handleFilterToggle = useCallback(() => {
    handleFilterStateChange(!isFilterOpen);
  }, [isFilterOpen, handleFilterStateChange]);

  const handleClearFilters = useCallback(
    (fn: (value: string) => void) => {
      setClearFilterFunction(() => (value?: string) => {
        if (!value) return;
        if (value === "ALL") {
          handleClearAllFilters();
          return;
        }

        fn(value);
      });
    },
    [handleClearAllFilters],
  );

  return (
    <div className="flex flex-col w-full">
      <div className="flex w-full items-center justify-between px-4 mt-4">
        <div className="w-1/4" />
        <div className="bg-white w-1/2 rounded-md">
          <Input placeholder="Replace with search transaction component" />
        </div>
        <div className="flex gap-5 w-1/4 justify-end mr-4">
          <Badge
            selected={false}
            className="bg-[#BDE4B2] py-2 px-[10.2px] rounded-[100px]"
          >
            <div className="flex gap-2 items-center">
              <span className="text-xs text-[#2A5024]">Cashflow</span>
              <ExcludedCashFlowV2 className="w-6 h-6" fill="#2A5024" />
            </div>
          </Badge>
          <div
            className="w-[29.9px] h-[29.9px] bg-[#DFECFF] rounded-[78.371px] flex items-center justify-center cursor-pointer"
            onClick={handleFilterToggle}
          >
            <GroupIcon />
            {appliedCount > 0 && (
              <svg
                width="21"
                height="21"
                viewBox="0 0 21 21"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="absolute translate-x-[40%] -translate-y-[40%] w-5 h-5 rounded-full flex items-center justify-center z-10"
              >
                <g id="Group 1000002379">
                  <rect
                    x="1.28947"
                    y="1.28947"
                    width="18.4211"
                    height="18.4211"
                    rx="9.21053"
                    fill="#FEB737"
                    stroke=""
                    strokeWidth="1.57895"
                  />
                  <path
                    stroke="#FEB737"
                    strokeWidth="1.57895"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  {appliedCount !== undefined && (
                    <text
                      x="10.5"
                      y="14"
                      textAnchor="middle"
                      fill="black"
                      fontSize="10"
                      fontWeight="medium"
                    >
                      {appliedCount}
                    </text>
                  )}
                </g>
              </svg>
            )}
          </div>
        </div>
      </div>
      <div className="px-4 mt-2 w-full">
        {filterBadges?.length! > 0 && (
          <FilterBadges
            badges={filterBadges!}
            onRemoveFilter={(value) => {
              if (clearFilterFunction) {
                clearFilterFunction(value);
              }
            }}
          />
        )}
      </div>
      <TransactionsContainer
        transactions={transactions}
        changeCategoryAction={changeCategoryAction}
        updateTransaction={updateTransaction}
        fetchTransactionDocuments={fetchTransactionDocuments}
        deleteTransactionDocuments={deleteTransactionDocuments}
        uploadFile={handleUploadFile}
        defaultFilterState={isFilterOpen}
        initialFilters={currentFilters}
        onFilterStateChange={handleFilterStateChange}
        onFilterBadgesChange={handleFilterBadgesChange}
        onClearFilterFunc={handleClearFilters}
        onApplyAllFilters={handleApplyAllFilters}
        onResetUrlFilters={() => {
          if(pathname !== null) router.push(pathname);
          handleClearAllFilters();
        }}
        lastItemRef={lastItemRef}
        hasMore={hasMore}
        onCategoriesData={handleCategoriesData}
        members={membersState}
        groups={groupsState}
      />
    </div>
  );
}

export default TransactionsClientContainer;
