import { format } from "date-fns";
import {
  Transaction
} from "./types/transaction.types";
import { Account, TransactionCard, TransactionGroup } from "@repo/ui";

interface GroupTransactionData {
  [id: string]: TransactionCard[];
}

export function groupTransactionsByDate(
  transactions: Transaction[],
  accounts: Account[]
): TransactionGroup[] {
  const groupedData: GroupTransactionData = {};
  const formattedTransactions: TransactionGroup[] = [];
  
  // Sort by timestamp
  transactions.sort(
    (a, b) => Number(b.txnTimestamp) - Number(a.txnTimestamp)
  );

  for (const transaction of transactions) {
    const dateValue = transaction.txnTimestamp;

    if (dateValue) {
      const formattedDate = formatDateFromTimestamp(dateValue);

      if (formattedDate === "Invalid Date") {
        continue;
      }

      if (!groupedData[formattedDate]) {
        groupedData[formattedDate] = [];
      }
      groupedData[formattedDate].push(
        formatTransactionCard(transaction, accounts)
      );
    } else {
      console.warn("Transaction missing date:", transaction);
    }
  }

  for (const date in groupedData) {
    if (groupedData.hasOwnProperty(date)) {
      formattedTransactions.push({
        date: new Date(date),
        transactions: groupedData[date] as TransactionCard[],
      });
    }
  }

  return formattedTransactions;
}

function formatDateFromTimestamp(timestampString: string) {
  const timestamp = parseInt(timestampString, 10);
  if (isNaN(timestamp)) {
    console.error("Invalid timestamp string:", timestampString);
    return "Invalid Date";
  }
  const dateObj = new Date(timestamp * 1000);
  if (isNaN(dateObj.getTime())) {
    console.error("Invalid date object from timestamp:", timestamp);
    return "Invalid Date";
  }
  return format(dateObj, "yyyy-MM-dd");
}

function formatTransactionCard(
  transaction: Transaction,
  accounts: Account[]
): TransactionCard {
  // Find the account for this transaction
  let account = accounts.find(
    (item) => item.accountId === transaction.accountId
  );

  // If account is not found, create a default account with the required properties
  if (!account) {
    account = {
      accountId: transaction.accountId || "",
      linkedAccRef: "",
      maskedAccNumber: "",
      fipId: transaction.fipId || "",
      holderName: "",
      branch: "",
      fiType: "",
      userId: "",
      dataSyncedAt: ""
    };
  }

  // Use camelCase properties for the tag object
  const tag = transaction.tag ? {
    categoryCollection: transaction.tag.categoryCollection,
    categoryId: transaction.tag.categoryId,
    subcategoryId: transaction.tag.subcategoryId
  } : null;

  // Create the transaction card with camelCase properties
  const newTxn: TransactionCard = {
    tag: tag,
    account: account,
    txnId: transaction.txnId,
    txnMode: transaction.mode,
    txnType: transaction.type,
    amount: transaction.amount,
    merchantName: transaction.merchant ? transaction.merchant.merchantName : "",
    bankName: setBankName(account?.branch || ""),
    isSaved: transaction.favorite,
    isExcludedCashflow: transaction.excludeCashFlow,
    cashFlowPeriod: transaction.cashFlowPeriod,
    narration: transaction.narration,
    documentsCount: transaction.documentsCount,
    notes: transaction.userNotes,
  };

  return newTxn;
}

function setBankName(branch: string) {
  const banks = ["icici", "hdfc", "axis", "sbi"];

  for (const bank of banks) {
    if (branch.toLowerCase().includes(bank)) {
      return bank;
    }
  }

  return "";
}
