import { FetchDocuments } from "@repo/ui";
import { createGrpcClient } from "./utils/create-grpc-client";
import { GRPC_CONFIG } from "./constants";
import {
  DepositTransactionClient,
  GetTransactionResponse,
  TransactionFilters,
} from "./types/transaction.types";
import { applyTransactionFilters } from "./utils/transaction-filters";
import * as grpc from "@grpc/grpc-js";

const transactionClient: DepositTransactionClient = createGrpcClient<DepositTransactionClient>({
  protoPath: GRPC_CONFIG.PROTO_FILES.TRANSACTION,
  servicePath: 'backend_services.visualization',
  serviceConstructor: 'DepositTransaction'
});

export function getTransactions(
  authToken: string,
  pageSize: number,
  pageNumber: number,
  filters?: TransactionFilters
): Promise<GetTransactionResponse> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);
  const request = applyTransactionFilters(filters || {});
  request.pagination_params = {
    page_size: pageSize,
    page_number: pageNumber,
  };

  return new Promise((resolve, reject) => {
    transactionClient.FetchDepositTxns(
      request,
      metadata,
      (error, response) => {
        if (error) {
          console.error("FetchDepositTxns error:", error);
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function saveTransaction(
  authToken: string,
  transaction_id: string,
  favorite: boolean,
): Promise<object> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);
  return new Promise((resolve, reject) => {
    transactionClient.MarkDepositTxnFavorite(
      {
        transaction_id,
        favorite,
      },
      metadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function assignCategoryToTransaction(
  authToken: string,
  transaction_id: string,
  category_id: string,
  subcategory_id: string,
  collection: string = "",
): Promise<object> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.AssignCategoryToDepositTxns(
      {
        transaction_ids: [transaction_id],
        category_id,
        subcategory_id,
        collection,
      },
      metadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function excludeTransactionFromCashFlow(
  authToken: string,
  transaction_id: string,
  flag: boolean,
): Promise<object> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.ExcludeTxnFromCashFlow(
      {
        txn_id: transaction_id,
        exclude_cash_flow: flag,
      },
      metadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function updateTransactionNotes(
  authToken: string,
  transaction_id: string,
  notes: string,
): Promise<object> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.UpdateTransactionNotes(
      {
        transaction_id,
        notes,
      },
      metadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function fetchTransactionDocumentsRPC(
  authToken: string,
  transaction_id: string,
): Promise<FetchDocuments> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.FetchTransactionDocuments(
      {
        transaction_id,
      },
      metadata,
      (error, response) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      },
    );
  });
}

export function deleteTransactionDocumentsRPC(
  authToken: string,
  transaction_id: string,
  fileNames: string[],
): Promise<{ ok: boolean }> {
  const metadata = new grpc.Metadata();
  metadata.set("authorization", authToken);

  return new Promise((resolve, reject) => {
    transactionClient.DeleteTransactionDocuments(
      {
        transaction_id,
        object_names: fileNames,
      },
      metadata,
      (error) => {
        if (error) {
          reject(error);
        } else {
          resolve({
            ok: true,
          });
        }
      },
    );
  });
}
