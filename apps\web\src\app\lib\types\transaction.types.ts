import * as grpc from "@grpc/grpc-js";
import {
  Account,
  Category,
  FetchDocuments,
  Subcategory,
  TransactionTypeEnum,
} from "@repo/ui";

export interface TransactionCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

export interface TransactionMerchant {
  userId: string;
  merchantId: string;
  merchantName: string;
}

export interface Transaction {
  txnId: string;
  accountId: string;
  amount: number;
  type: TransactionTypeEnum;
  txnTimestamp: string;
  mode: string;
  narration: string;
  rawTxnId: string;
  favorite: boolean;
  tag: TransactionCategory;
  excludeCashFlow: boolean;
  merchant: TransactionMerchant;
  fipId: string;
  userNotes: string;
  cashFlowPeriod: {
    month: number;
    year: number;
  };
  documentsCount: number;
  account?: Account;
  category?: Category;
  subcategory?: Subcategory;
}

export interface GetTransactionResponse {
  cards: Transaction[];
}

export interface TransactionRequest {
  filter: {
    user_groups?: {
      user_ids?: { id: string }[];
      user_group_ids?: { id: string }[];
    };
    account_filters?: {
      account_ids: string[];
    };
    txn_filters?: {
      time_range?: {
        from_time?: number;
        to_time?: number;
      };
      amount_range?: {
        min_amount?: number;
        max_amount?: number;
      };
      favorited?: { favorited: boolean };
      exclude_cash_flow?: { exclude_cash_flow: boolean };
      has_user_notes?: { has_user_notes: boolean };
      txn_type?: { txn_type: string };
      untagged?: { untagged: boolean };
      category?: Array<{
        category_collection: string;
        category_id: string;
        subcategory_id: string;
      }>;
    };
  };
  pagination_params: {
    page_size: number;
    page_number: number;
  };
}

export interface TransactionFilters {
  userGroups?: {
    userIds: { id: string }[];
    userGroupIds: { id: string }[];
  };
  accountFilters?: { accountIds?: string[] };
  timeRange?: { fromTime: number; toTime: number };
  amountRange?: { minAmount: number; maxAmount: number };
  transactionType?: string;
  tagStatus?: string;
  bookmarkOptions?: {
    showFavorites?: boolean;
    excludedFromCashflow?: boolean;
    withNotes?: boolean;
  };
  categories?: Array<TransactionCategory>;
}

export type DepositTransactionClient = grpc.Client & {
  FetchDepositTxns: (
    request: object,
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: GetTransactionResponse,
    ) => void,
  ) => void;
  MarkDepositTxnFavorite: (
    request: {
      transaction_id: string;
      favorite: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  ExcludeTxnFromCashFlow: (
    request: {
      txn_id: string;
      exclude_cash_flow: boolean;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  UpdateTransactionNotes: (
    request: {
      transaction_id: string;
      notes: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  FetchTransactionDocuments: (
    request: {
      transaction_id: string;
    },
    metadata: grpc.Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: FetchDocuments,
    ) => void,
  ) => void;
  DeleteTransactionDocuments: (
    request: {
      transaction_id: string;
      object_names: string[];
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
  AssignCategoryToDepositTxns: (
    request: {
      transaction_ids: string[];
      category_id: string;
      collection: string;
      subcategory_id: string;
    },
    metadata: grpc.Metadata,
    callback: (error: grpc.ServiceError | null, response: object) => void,
  ) => void;
};