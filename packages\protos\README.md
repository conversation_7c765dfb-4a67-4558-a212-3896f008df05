# @repo/protos

Protocol Buffer definitions and TypeScript type generation for the CuspWeb project.

## 🚀 Quick Start

The visualization proto types are automatically generated when you run `pnpm install` or `pnpm build`. No manual setup required!

## 📁 Structure

```
packages/protos/
├── backend_services/     # Backend service definitions
│   ├── visualization/    # Visualization service protos
│   ├── user/            # User service protos
│   ├── data_access/     # Data access service protos
│   └── external/        # External service protos
├── database/            # Database schema protos
├── rebit/              # Rebit integration protos
├── workflows/          # Workflow definitions
├── common/             # Common/shared protos
├── types/              # Generated TypeScript types (auto-generated)
└── utils.proto         # Utility definitions
```

## 🔧 Commands

- `pnpm dev` - Generate TypeScript types from proto files with development server
- `pnpm proto:gen` - Generate TypeScript types from proto files
- `pnpm clean` - Remove generated types

## 📦 Usage in Web App

### Direct imports (Recommended)
```typescript
import type {
  Tag,
  DepositTransactionCard,
  FetchDepositTxnsRequest,
  FetchDepositTxnsResponse,
  TimeStep,
  ApplyUserFiltersRequest,
  RecurrentTxnType,
  MetadataRequest
} from '@repo/protos/types/backend-service/visualization/deposit_transaction';
```

## 🔄 Auto-Generation

Types are automatically generated:
- ✅ During `pnpm dev` 
- ✅ When running `pnpm proto:gen`

## 🌐 Cross-Platform

The generation process works on:
- ✅ Windows
- ✅ macOS
- ✅ Linux

## 📝 Generated Files

- `types/` - All generated TypeScript types
- Individual `.ts` files matching the `.proto` structure

## ✨ Key Features

- **🚫 Conflict-Free**: Excludes `protobufPackage` and internal constants
- **🎯 Focused Scope**: Only visualization proto files + their dependencies
- **🔄 Auto-Regeneration**: Updates automatically when proto files change

## 🚨 Important Notes

- The `types/` directory is auto-generated and should not be edited manually
- The `types/` directory is gitignored and regenerated on each install
- Only visualization proto files are processed for focused type generation
- Types are dynamically discovered and exported to avoid naming conflicts