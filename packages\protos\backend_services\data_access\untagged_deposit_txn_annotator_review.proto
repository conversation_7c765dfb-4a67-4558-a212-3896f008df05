syntax = "proto3";
package backend_services.data_access;

import "database/fi_deposit.proto";

service UntaggedDepositTxnAnnotatorReview {
    rpc GetUntaggedGroupedTransactions(GetUntaggedGroupedTransactionsRequest) returns (GetUntaggedGroupedTransactionsResponse) {};
    rpc MarkUntaggedGroupProcessedByAnnotator(MarkUntaggedGroupProcessedByAnnotatorRequest) returns (MarkUntaggedGroupProcessedByAnnotatorResponse) {};
    rpc MarkUntaggedGroupProcessedByReviewer(MarkUntaggedGroupProcessedByReviewerRequest) returns (MarkUntaggedGroupProcessedByReviewerResponse) {};
}

message GetUntaggedGroupedTransactionsRequest{}
message GetUntaggedGroupedTransactionsResponse{
    repeated database.UntaggedDepositTxnAnnotatorReview groups = 1;
}

message MarkUntaggedGroupProcessedByAnnotatorRequest{
    string _id = 1;
}
message MarkUntaggedGroupProcessedByAnnotatorResponse{}

message MarkUntaggedGroupProcessedByReviewerRequest{
    string _id = 1;
}
message MarkUntaggedGroupProcessedByReviewerResponse{}