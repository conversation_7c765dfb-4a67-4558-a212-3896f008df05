syntax = "proto3";
package backend_services.visualization;

import "database/fi_deposit.proto";
import "database/cards_filters.proto";
import "backend_services/visualization/deposit_transaction.proto";

// Service for Recurrent Groups
service RecurrentGroups{
    rpc GetRecurrentTxns(GetRecurrentTxnsRequest) returns (GetRecurrentTxnsResponse) {};
    rpc GetCommonRecurrentGroups(GetCommonRecurrentGroupsRequest) returns (GetCommonRecurrentGroupsResponse) {};
    rpc GetRecurrentGroupDetails(GetRecurrentGroupDetailsRequest) returns (GetRecurrentGroupDetailsResponse) {};
    rpc MarkRecurrentGroupFavorite(MarkRecurrentGroupFavoriteRequest) returns (MarkRecurrentGroupFavoriteResponse) {};
    rpc ExcludeRecurrentGroupFromCashFlow(ExcludeRecurrentGroupFromCashFlowRequest) returns (ExcludeRecurrentGroupFromCashFlowResponse) {};
    rpc DeleteRecurrentGroup(DeleteRecurrentGroupRequest) returns (DeleteRecurrentGroupResponse) {};
    rpc AddTxnToRecurrentGroup(AddTxnToRecurrentGroupRequest) returns (AddTxnToRecurrentGroupResponse) {};
    rpc RemoveTxnFromRecurrentGroup(RemoveTxnFromRecurrentGroupRequest) returns (RemoveTxnFromRecurrentGroupResponse) {};   
}


message RecurrentGroupFilters {
    database.TxnFilterTimeRange time_range = 1;

    database.UserGroupFilters user_groups = 2;
    database.AccountsFilter account_filters = 3;
    database.RecurrentTxnGroupFilters group_filters = 4;
    database.TxnFilterTxnType txn_type = 5;
    repeated database.TxnFilterCategory txn_categories = 6;
    database.TxnFilterAmountRange txn_amount_range = 7;
    
}

enum SortBy {
    SORT_BY_UNSPECIFIED = 0;
    SORT_BY_AMOUNT_ASCENDING = 1;
    SORT_BY_AMOUNT_DESCENDING = 2;
}

message RecurrentGroupSummary {
    string recurrent_group_id = 1;
    Merchant merchant = 2;
    Tag tag = 3;
    string account_id = 4;
    string user_id = 5;
    double amount = 6;
    string frequency = 7;
    repeated string dates = 8;
    bool favorite  = 9;
    bool exclude_cash_flow = 10;
    string txn_mode = 11;
}
message CommonRecurrentGroupCard {
    Merchant merchant = 1;
    double amount = 2;
    repeated string user_ids = 3;
    string frequency = 4;
    repeated string dates = 5;

    repeated RecurrentGroupSummary summaries = 6;
}

message GetCommonRecurrentGroupsRequest {
    RecurrentGroupFilters filters = 1;
    SortBy sort_by = 2;
}
message GetCommonRecurrentGroupsResponse {
    repeated CommonRecurrentGroupCard cards = 1;
}

message GetRecurrentTxnsRequest {
    RecurrentGroupFilters filters = 1;
    SortBy sort_by = 2;
}
message GetRecurrentTxnsResponse {
    repeated RecurrentGroupSummary summaries = 1;
    repeated RecurrentGroupTxnCard txns = 2;
}


enum RecurrentTxnType {
    RECURRENT_TXN_TYPE_UNSPECIFIED = 0;
    RECURRENT_TXN_TYPE_PAID = 1;
    RECURRENT_TXN_TYPE_UPCOMING = 2;
    RECURRENT_TXN_TYPE_MISSED = 3;
} 

message RecurrentGroupTxnCard {
    RecurrentTxnType recurrent_txn_type = 1;
    DepositTransactionCard txn_card = 2;
}

message GetRecurrentGroupDetailsRequest {
    string recurrent_group_id = 1;
}
message GetRecurrentGroupDetailsResponse {
    RecurrentGroupSummary summary = 1;
    repeated RecurrentGroupTxnCard txns = 2;
}



message MarkRecurrentGroupFavoriteRequest{
    string recurrent_group_id = 1;
    bool favorite = 2;
}
message MarkRecurrentGroupFavoriteResponse {}


message ExcludeRecurrentGroupFromCashFlowRequest{
    string recurrent_group_id = 1;
    bool exclude_from_cash_flow = 2;
}
message ExcludeRecurrentGroupFromCashFlowResponse{}


message DeleteRecurrentGroupRequest {
    string recurrent_group_id = 1;
}
message DeleteRecurrentGroupResponse {}


message RemoveTxnFromRecurrentGroupRequest {
    string txn_id = 1;
    string recurrent_group_id = 2;
}
message RemoveTxnFromRecurrentGroupResponse {}


message AddTxnToRecurrentGroupRequest {
    string txn_id = 1;
    string recurrent_group_id = 2;
}
message AddTxnToRecurrentGroupResponse {}

