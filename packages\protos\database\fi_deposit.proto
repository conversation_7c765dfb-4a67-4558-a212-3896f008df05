syntax = "proto3";
package database;

import "rebit/fi_schemas/deposit/deposit.proto";
import "database/custom_type.proto";
import "shared/shared.proto";


// flag:is-collection=true;
message FiDepositAccountSummary {
    ObjectId _id = 1;

    // FIP Identifier
    string fip_id = 2;

    // Financial information type
    string fi_type = 3;

    // TODO: We receive accType in response of consent status finve http call, do we need to save it ?

    // Masked account number
    string masked_acc_number = 5;

    // Unique account number associated with aa account. 
    string linked_acc_ref = 6;
  
    // Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type.
    rebit.fi_schemas.deposit.Profile profile = 7;

    // The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account.
    rebit.fi_schemas.deposit.Summary summary = 8;
    
    // Document creation timestamp
    int64 created_at = 9;

    // Document modification timestamp
    int64 updated_at = 10;

    FiDepositAccountInitialSummary initial_summary = 11;
}

message FiDepositAccountInitialSummary {
    rebit.fi_schemas.deposit.Summary summary = 1;
    int64 last_transaction_timestamp = 2;  // @gotags: bson:"last_transaction_timestamp"
}

// flag:is-collection=true;
message FiDepositAccountTransaction {
    ObjectId _id = 1;  

    // FIP Identifier
    string fip_id = 2;

    // Financial information type
    string fi_type = 3;

    // _id of account summary record in database
    ObjectId account_id = 5; 

    // Masked account number
    string masked_acc_number = 6;
  
    // Details of all transactions that have been posted in an account.
    rebit.fi_schemas.deposit.Transaction transaction = 7;
  
    // Document creation timestamp
    int64 created_at = 8;

    // Document modification timestamp
    int64 updated_at = 9;
}
  
// flag:is-collection=true;
message DepositAccountDailyClosingBalance {
    ObjectId _id = 1;  
    ObjectId account_id = 2; 
    int64 summary_date = 3;
    string closing_balance = 4;
    int64 last_processed_transaction_time = 5; // @gotags: bson:"last_processed_transaction_time"
    int64 created_at = 6;
    int64 updated_at = 7;
}


// flag:is-collection=true;
message DepositTxnCategoryMetadata {
    // Value of _id field is not autogenerated, but is populated to be same as value of _id field of corresponding doc from FiDepositAccountTransaction collection.
    ObjectId _id = 1;   
    ObjectId account_id = 2; 
    string status = 3; // Tagging state
    string transaction_narration = 4; // Remark
    string auto_transaction_category_id = 5; //   _id of the category doc that this transaction belongs to
    repeated string entity = 6; // Who
    repeated string purpose = 7; // Why
    string ifsc = 8; // IFSC Code of entity, if available
    string notes = 9; // Additional notes generated by tagging
    string matched_pattern = 10; // Pattern found in tagging
    string annotation_tool_status = 11; // Retool processing status
    int64 created_at = 12;
    int64 updated_at = 13; 
}

message CashFlowPeriod {
    int32 month = 1;
    int32 year = 2; 
}

message TxnFilters {
    TxnFilterTimeRange time_range = 1;
    repeated TxnFilterCategory category = 2;
    TxnFilterAmountRange amount_range = 3;
    repeated TxnFilterMerchant merchant = 4;
    TxnFilterNarration narration = 5;
    TxnFilterFavorited favorited = 6;
    TxnFilterUntagged untagged = 7;
    TxnFilterHasUserNotes has_user_notes = 8;
    TxnFilterExcludeCashFlow exclude_cash_flow = 9;
    TxnFilterTxnType txn_type = 10;
    TxnFilterHasRecurrentGroup has_recurrent_group = 11;
    TxnFilterNotes notes = 12;
    repeated TxnFilterRecurrentGroup recurrent_groups = 13;
}

message TxnFilterTimeRange {
    int64 from_time = 1;
    int64 to_time = 2;
}

message TxnFilterCategory {
    string category_collection = 1;
    string category_id = 2;
    string subcategory_id = 3;
}

message TxnFilterAmountRange {
    int64 min_amount = 1;
    int64 max_amount = 2;
    bool include_for_search = 3; // Whether to include this filter in search
}

message TxnFilterMerchant {
    string merchant_id = 1;
}

message TxnFilterNarration {
    string narration = 1;
}

message TxnFilterTxnType {
    string txn_type = 1;
}

message TxnFilterFavorited {
    bool favorited = 1;
}

message TxnFilterUntagged {
    bool untagged = 1;
}

message TxnFilterHasUserNotes {
    bool has_user_notes = 1;
}

message TxnFilterExcludeCashFlow {
    bool exclude_cash_flow = 1;
}

message TxnPaginationParams {
    uint32 page_size = 1;
    uint32 page_number = 2;
}

message TxnFilterHasRecurrentGroup {
    bool has_recurrent_group = 1;
}

message TxnFilterRecurrentGroup {
    string recurrent_group_id = 1;
}

message TxnFilterNotes {
    string notes = 1;
}

enum DepositTxnCategoryUpdateSource {
    NONE = 0;
    MONGO_KEYWORD = 1;
    LLM = 2;
    USER = 3;
    USER_PERSONALISED = 4;
}

// flag:is-collection=true;
// TODO: Have taken it from: https://github.com/cusp-money/protos/pull/109/files
message DepositTransaction {
    ObjectId _id = 1; // auto-generated
    ObjectId txn_id = 2; // same as _id field of corresponding doc from FiDepositAccountTransaction
    int64 timestamp = 3; // when the record was updated
    UpdateSource update_source = 4; // who created this update
    FiDepositAccountTransaction raw_transaction = 5; // raw transaction

    string status = 6; // Tagging status
    repeated string entity = 7; // Parsed Who
    repeated string purpose = 8; // Parsed Why
    string ifsc = 9; // IFSC Code of parsed entity, if available
    string user_notes = 10; // Notes provided by user
    string matched_pattern = 11; // Pattern found in parsing

    string category_id = 12; // Txn category id
    string subcategory_id = 13; // Txn subcategory id
    string category_collection = 14; // global or <uid>
    DepositTxnCategoryUpdateSource category_update_source = 210; // Source that updated fields `category_id`` and `subcategory_id`
    int64 category_id_updated_at = 110; // Timestamp at which fields category_id field was updated

    string merchant_id = 15; // Txn merchant id

    bool exclude_cash_flow = 16; // Exclude txn from all cash flow analysis
    bool favorite = 17; // User has favorited/bookmarked transaction
    
    int64 created_at = 18;
    
    string prediction_reason = 19; // Reason of current tagging prediction. Generated by tagging service.
    CashFlowPeriod cashflow_account_in_period = 20; // Txn accounted in which period in cashflow
    repeated Document documents = 21; // User attached documents relevant for this transaction
    ObjectId recurrent_group_id = 22; // Id of recurrent transaction group this belongs to

    repeated string identifier_keywords = 23;

    enum UpdateSource {
        NONE = 0;
        SYSTEM = 1;
        USER = 2;
    }

    message Document {
        string object_name = 1; // Mirror of cloud storage object name
        string file_name = 2; // file name
        string bucket = 3; // Bucket where file is stored
        string thumbnail = 4; // Base64 encoded thumbnail for images
        string content_type = 5; // MIME type of the document
    }
    
}

// flag:is-collection=true;
message RecurrentTransactionGroup {
    ObjectId _id = 1; // auto-generated
    ObjectId account_id = 2; // One group per account  
    ObjectId subcategory_id = 3; // Subtag
    ObjectId category_id = 4; // Tag
    string txn_type = 5; // "CREDIT" or "DEBIT"
    string txn_mode = 6; // Most recent/frequent payment mode e.g. Auto Debit, UPI, NEFT
    RecurrentGroupStartTime start_time = 8; // Start time of recurrent group
    RecurrentGroupFrequency frequency = 9; // Frequency of recurrence
    ObjectId merchant_id = 10; // Most recent/frequent merchant_id of the group
    bool favorite  = 11; // Group saved by user
    bool exclude_cash_flow = 12; // All txns of group excluded from cash flow
    bool completed = 13; // The group has been marked as completed (e.g. all installments of EMI paid)
    string user_notes = 15; // Notes provided by user 
    int64 end_date = 14; // Auto-derived or user-provided date, by which the group would complete 
    int64 created_at = 16;
    int64 updated_at = 17;
}


message RecurrentGroupStartTime {
    // Month of Year (e.g. January) - Every quarter, with quarter starting at January
    // Day of Month (e.g. 12th) - Every month, with month starting at 12th 
    shared.Date date = 1; 
    
    // Day of Week (e.g. Monday) - Every Monday 
    shared.DayOfWeek day_of_week = 2;
}

message RecurrentGroupFrequency {
    // E.g. (repeats:2 period:month) - Txn repeats every 2 months
    int32 repeats = 1;
    shared.CalendarPeriod period = 2; 
}

message RecurrentTxnGroupFilters {
    repeated RecurrentGroupFrequency frequency = 1;
    RecurrentGroupFilterFavorite favorite = 2;
    RecurrentGroupFilterExcludeCashFlow exclude_cash_flow = 3;
}

message RecurrentGroupFilterFavorite {
    bool favorite = 1;
}

message RecurrentGroupFilterExcludeCashFlow {
    bool exclude_cash_flow = 1;
}


// flag:is-collection=true;
message DeletedFiDepositAccountSummary {}

// flag:is-collection=true;
message DeletedFiDepositAccountTransaction {}

// flag:is-collection=true;
message DeletedFiDepositTxnCategoryMetadata{}

// flag:is-collection=true;
message DepositTxnUserPersonalizedTagFreq {
    ObjectId _id = 1; // auto-generated
    string keyword = 2;
    string category_id = 3;
    string subcategory_id = 4;
    int64 frequency = 5;
    string type = 6; // credit or debit 
    ObjectId user_id = 7; // system user id
    string family_id = 8; // family id
    int64 created_at = 101;
    int64 updated_at = 102;
}

// flag:is-collection=true;
message DepositTxnLlmPredictedTagFreq{
    ObjectId _id = 1; // auto-generated
    string keyword = 2;
    string category_id = 3;
    string subcategory_id = 4;
    string type = 5; // credit or debit
    int64 frequency = 6;
    int64 created_at = 101;
    int64 updated_at = 102;
}

// flag:is-collection=true;
message UntaggedDepositTxnAnnotatorReview{
    ObjectId _id = 1; // auto-generated
    string keyword = 2;
    string type = 3; // credit or debit
    repeated string txn_narrations = 4;
    int64 txn_count = 5;
    int64 tagged_txn_whose_tags_will_change = 6;
    bool is_processed_by_annotator = 8;
    bool is_processed_by_reviewer = 9;
    int64 created_at = 101;
    int64 updated_at = 102;
}