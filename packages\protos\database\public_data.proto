syntax = "proto3";

package database;
import "database/custom_type.proto";

// flag:is-collection=true;
message DepositoryParticipantInfo {
    ObjectId _id = 1;
    string name = 2;
    string dp_id = 3;
    string depository_name = 4;
    string email = 5;
    string contact_no = 6;
    string address = 7;
    string website = 8;
}

// flag:is-collection=true;
message ETFPrice {
    ObjectId _id = 1;
    string stock_exchange = 2;
    int64 date = 3;
    string isin_code = 4;
    string open_price = 5;
    string high_price = 6;
    string low_price = 7;
    string nav_price = 8;
    string last_traded_price = 9;
    int64 created_at = 10;
    int64 updated_at = 11;
}

// flag:is-collection=true;
message ETFInfo {
    ObjectId _id = 1;
    string stock_exchange = 2;
    string symbol = 3;
    string company_name = 4;
    string isin_code = 5;
    int64 created_at = 6;
    int64 updated_at = 7;
}

// flag:is-collection=true;
message MutualFundInfo {
    ObjectId _id = 1;
    string scheme_code = 2;
    string amc_name = 3;
    string scheme_name = 4;
    string scheme_category = 5;
    string scheme_type = 6;
    string isin_payout_growth = 7;
    string isin_reinvestment = 8;
    string scheme_subcategory = 9;
    string plan = 10;
    string option = 11;
    string idcw_type = 12;
    int64 created_at = 13;
    int64 updated_at = 14;
}
  
// flag:is-collection=true;
message MutualFundNAV {
    ObjectId _id = 1;
    string isin = 2;
    int64 date = 3;
    string nav_price = 4;
    int64 created_at = 5;
    int64 updated_at = 6;
}

// flag:is-collection=true;
message StockPrice {
    ObjectId _id = 1;
    string stock_exchange = 2;
    int64 date = 3;
    string isin_code = 4;
    string open_price = 5;
    string high_price = 6;
    string low_price = 7;
    string close_price = 8;
    string last_price = 9;
    int64 created_at = 10;
    int64 updated_at = 11;
}

// flag:is-collection=true;
message StockInfo {
    ObjectId _id = 1;
    string isin_code = 2;
    string stock_exchange = 3;
    string tracker_symbol = 4;
    string financial_instrument_id = 5;
    string financial_instrument_name = 6;
    string image_path = 7;
    int64 created_at = 8;
    int64 updated_at = 9;
}

// flag:is-collection=true;
message StockDividend {
    ObjectId _id = 1;
    string isin_code = 2;
    string symbol = 3;
    string company_name = 4;
    int64 ex_date = 5;
    string dividend = 6;
}

// flag:is-collection=true;
message EquityCorporateAction{
    ObjectId _id = 1;
    string isin = 2;
    string execution_date = 3;
    string record_date =4;
    string raw_subject = 5;
    string company_name = 6;
    string face_value = 7;
    WrapOneOfEquityCorporateActionRecord corporate_action = 8;
    int64 created_at = 9;
    int64 updated_at = 10;
}

message WrapOneOfEquityCorporateActionRecord{
    oneof action{
        StockDividendAction stock_dividend = 1;
        BonusIssue bonus_issue = 2;
        StockSplit stock_split = 3;
        RightsIssue rights_issue = 4;
        AmountDistribution distribution = 5;
        MeetingNotification meeting = 6;
        InterestPayment interest = 7;
        UnknownAction unknown = 8;
    }
}

message StockDividendAction{
    // "Interim Dividend - Rs 10 Per Share"
    // Dividend amount 10 Rs. per share
    string dividend_amount = 1;
}

message BonusIssue{
    // "Bonus Preference Shares 21:1"
    // Per 21 shares
    string per_n_share = 1;
    // 1 additional share
    string additional_share = 2;
}

message StockSplit{
    // "Face Value Split From Rs 10/- To Rs 2/-"
    // Split from Rs 10/-
    string split_from = 1;
    // To Rs 2/-
    string split_to = 2;
}

message RightsIssue{
    // "Rights 91:20 @ Premium Rs.10/- Per Share" 
    // Per 20 shares
    string per_n_share = 1;
    // 91 shares can be purchased
    string new_n_share = 2;
    // at Premium of Rs.10/- per share.
    string premium_amount = 3;
}

message AmountDistribution{}

message MeetingNotification{
    string book_closure_start_date = 1;
    string book_closure_end_date = 2;
}

message InterestPayment{}

message UnknownAction{}


message PriceChange {
    float one_day_percent_diff = 1;
    float one_week_percent_diff = 2;
    float one_month_percent_diff = 3;
    int64 change_date = 4;
}

message ExchangeLevelStatistics{
    float pe_ratio = 1;
    PriceChange change = 2;
}

// flag:is-collection=true;
message StockStatistics{
    string isin = 1;
    ExchangeLevelStatistics nse = 2;
    ExchangeLevelStatistics bse = 3;
    StockInfoGroww stock_info_groww = 4;
    int64 created_at = 10;
    int64 updated_at = 11;
}

message StockInfoGroww{
    string nse_code = 1;
    string bse_code = 2;
    float market_cap = 3;
    string industry = 4;
    string capped_type = 5;
    bool is_bse_tradable = 6;
    bool is_nse_tradable=7;
    string logo_url=8;
}

// flag:is-collection=true;
message ETFInfoGroww{
    string isin = 1;
    string nse_code = 2;
    string bse_code = 3;
    float aum_in_cr = 4;
    string category = 5;
    bool is_bse_tradable = 6;
    bool is_nse_tradable=7;
    string benchmark=8;
    string business_summary=9;
    string logo_url=10;
    int64 created_at = 11;
    int64 updated_at = 12;
}
