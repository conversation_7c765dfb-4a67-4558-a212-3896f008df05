#!/usr/bin/env node

import { execSync } from 'child_process';
import  fs from 'fs';
import  path from 'path';
import * as glob from 'glob';

// Ensure types directory exists
if (!fs.existsSync('types')) {
  fs.mkdirSync('types', { recursive: true });
}

// Determine the correct protoc-gen-ts_proto path based on platform
const isWindows = process.platform === 'win32';
const pluginPath = isWindows
  ? path.resolve('protoc-gen-ts_proto.cmd')
  : path.resolve('node_modules/ts-proto/protoc-gen-ts_proto');

// Find only visualization proto files
const protoFiles = glob.sync('backend_services/visualization/*.proto');

if (protoFiles.length === 0) {
  console.log('⚠️  No visualization proto files found');
  process.exit(0);
}

console.log(`📁 Found ${protoFiles.length} visualization proto files:`, protoFiles);

// Build the protoc command
const protocCmd = [
  'npx @protobuf-ts/protoc',
  `--plugin=protoc-gen-ts_proto=${pluginPath}`,
  '--ts_proto_out=types',
  '--ts_proto_opt=esModuleInterop=true,keepCase=true,outputServices=grpc-js',
  '-I .',
  ...protoFiles
].join(' ');


console.log('🔄 Generating TypeScript types from proto files...');

try {
  execSync(protocCmd, { stdio: 'inherit' });
  console.log('✅ Proto types generated successfully!');
} catch (error) {
  console.error('❌ Failed to generate proto types:', error.message);
  process.exit(1);
}

