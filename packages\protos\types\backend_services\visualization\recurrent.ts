// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.2
//   protoc               v6.31.1
// source: backend_services/visualization/recurrent.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import {
  type CallOptions,
  ChannelCredentials,
  Client,
  type ClientOptions,
  type ClientUnaryCall,
  type handleUnaryCall,
  makeGenericClientConstructor,
  Metadata,
  type ServiceError,
  type UntypedServiceImplementation,
} from "@grpc/grpc-js";
import { AccountsFilter, UserGroupFilters } from "../../database/cards_filters";
import {
  RecurrentTxnGroupFilters,
  TxnFilterAmountRange,
  TxnFilterCategory,
  TxnFilterTimeRange,
  TxnFilterTxnType,
} from "../../database/fi_deposit";
import { DepositTransactionCard, Merchant, Tag } from "./deposit_transaction";

export const protobufPackage = "backend_services.visualization";

export enum SortBy {
  SORT_BY_UNSPECIFIED = 0,
  SORT_BY_AMOUNT_ASCENDING = 1,
  SORT_BY_AMOUNT_DESCENDING = 2,
  UNRECOGNIZED = -1,
}

export function sortByFromJSON(object: any): SortBy {
  switch (object) {
    case 0:
    case "SORT_BY_UNSPECIFIED":
      return SortBy.SORT_BY_UNSPECIFIED;
    case 1:
    case "SORT_BY_AMOUNT_ASCENDING":
      return SortBy.SORT_BY_AMOUNT_ASCENDING;
    case 2:
    case "SORT_BY_AMOUNT_DESCENDING":
      return SortBy.SORT_BY_AMOUNT_DESCENDING;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SortBy.UNRECOGNIZED;
  }
}

export function sortByToJSON(object: SortBy): string {
  switch (object) {
    case SortBy.SORT_BY_UNSPECIFIED:
      return "SORT_BY_UNSPECIFIED";
    case SortBy.SORT_BY_AMOUNT_ASCENDING:
      return "SORT_BY_AMOUNT_ASCENDING";
    case SortBy.SORT_BY_AMOUNT_DESCENDING:
      return "SORT_BY_AMOUNT_DESCENDING";
    case SortBy.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RecurrentTxnType {
  RECURRENT_TXN_TYPE_UNSPECIFIED = 0,
  RECURRENT_TXN_TYPE_PAID = 1,
  RECURRENT_TXN_TYPE_UPCOMING = 2,
  RECURRENT_TXN_TYPE_MISSED = 3,
  UNRECOGNIZED = -1,
}

export function recurrentTxnTypeFromJSON(object: any): RecurrentTxnType {
  switch (object) {
    case 0:
    case "RECURRENT_TXN_TYPE_UNSPECIFIED":
      return RecurrentTxnType.RECURRENT_TXN_TYPE_UNSPECIFIED;
    case 1:
    case "RECURRENT_TXN_TYPE_PAID":
      return RecurrentTxnType.RECURRENT_TXN_TYPE_PAID;
    case 2:
    case "RECURRENT_TXN_TYPE_UPCOMING":
      return RecurrentTxnType.RECURRENT_TXN_TYPE_UPCOMING;
    case 3:
    case "RECURRENT_TXN_TYPE_MISSED":
      return RecurrentTxnType.RECURRENT_TXN_TYPE_MISSED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RecurrentTxnType.UNRECOGNIZED;
  }
}

export function recurrentTxnTypeToJSON(object: RecurrentTxnType): string {
  switch (object) {
    case RecurrentTxnType.RECURRENT_TXN_TYPE_UNSPECIFIED:
      return "RECURRENT_TXN_TYPE_UNSPECIFIED";
    case RecurrentTxnType.RECURRENT_TXN_TYPE_PAID:
      return "RECURRENT_TXN_TYPE_PAID";
    case RecurrentTxnType.RECURRENT_TXN_TYPE_UPCOMING:
      return "RECURRENT_TXN_TYPE_UPCOMING";
    case RecurrentTxnType.RECURRENT_TXN_TYPE_MISSED:
      return "RECURRENT_TXN_TYPE_MISSED";
    case RecurrentTxnType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface RecurrentGroupFilters {
  timeRange: TxnFilterTimeRange | undefined;
  userGroups: UserGroupFilters | undefined;
  accountFilters: AccountsFilter | undefined;
  groupFilters: RecurrentTxnGroupFilters | undefined;
  txnType: TxnFilterTxnType | undefined;
  txnCategories: TxnFilterCategory[];
  txnAmountRange: TxnFilterAmountRange | undefined;
}

export interface RecurrentGroupSummary {
  recurrentGroupId: string;
  merchant: Merchant | undefined;
  tag: Tag | undefined;
  accountId: string;
  userId: string;
  amount: number;
  frequency: string;
  dates: string[];
  favorite: boolean;
  excludeCashFlow: boolean;
  txnMode: string;
}

export interface CommonRecurrentGroupCard {
  merchant: Merchant | undefined;
  amount: number;
  userIds: string[];
  frequency: string;
  dates: string[];
  summaries: RecurrentGroupSummary[];
}

export interface GetCommonRecurrentGroupsRequest {
  filters: RecurrentGroupFilters | undefined;
  sortBy: SortBy;
}

export interface GetCommonRecurrentGroupsResponse {
  cards: CommonRecurrentGroupCard[];
}

export interface GetRecurrentTxnsRequest {
  filters: RecurrentGroupFilters | undefined;
  sortBy: SortBy;
}

export interface GetRecurrentTxnsResponse {
  summaries: RecurrentGroupSummary[];
  txns: RecurrentGroupTxnCard[];
}

export interface RecurrentGroupTxnCard {
  recurrentTxnType: RecurrentTxnType;
  txnCard: DepositTransactionCard | undefined;
}

export interface GetRecurrentGroupDetailsRequest {
  recurrentGroupId: string;
}

export interface GetRecurrentGroupDetailsResponse {
  summary: RecurrentGroupSummary | undefined;
  txns: RecurrentGroupTxnCard[];
}

export interface MarkRecurrentGroupFavoriteRequest {
  recurrentGroupId: string;
  favorite: boolean;
}

export interface MarkRecurrentGroupFavoriteResponse {
}

export interface ExcludeRecurrentGroupFromCashFlowRequest {
  recurrentGroupId: string;
  excludeFromCashFlow: boolean;
}

export interface ExcludeRecurrentGroupFromCashFlowResponse {
}

export interface DeleteRecurrentGroupRequest {
  recurrentGroupId: string;
}

export interface DeleteRecurrentGroupResponse {
}

export interface RemoveTxnFromRecurrentGroupRequest {
  txnId: string;
  recurrentGroupId: string;
}

export interface RemoveTxnFromRecurrentGroupResponse {
}

export interface AddTxnToRecurrentGroupRequest {
  txnId: string;
  recurrentGroupId: string;
}

export interface AddTxnToRecurrentGroupResponse {
}

function createBaseRecurrentGroupFilters(): RecurrentGroupFilters {
  return {
    timeRange: undefined,
    userGroups: undefined,
    accountFilters: undefined,
    groupFilters: undefined,
    txnType: undefined,
    txnCategories: [],
    txnAmountRange: undefined,
  };
}

export const RecurrentGroupFilters: MessageFns<RecurrentGroupFilters> = {
  encode(message: RecurrentGroupFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeRange !== undefined) {
      TxnFilterTimeRange.encode(message.timeRange, writer.uint32(10).fork()).join();
    }
    if (message.userGroups !== undefined) {
      UserGroupFilters.encode(message.userGroups, writer.uint32(18).fork()).join();
    }
    if (message.accountFilters !== undefined) {
      AccountsFilter.encode(message.accountFilters, writer.uint32(26).fork()).join();
    }
    if (message.groupFilters !== undefined) {
      RecurrentTxnGroupFilters.encode(message.groupFilters, writer.uint32(34).fork()).join();
    }
    if (message.txnType !== undefined) {
      TxnFilterTxnType.encode(message.txnType, writer.uint32(42).fork()).join();
    }
    for (const v of message.txnCategories) {
      TxnFilterCategory.encode(v!, writer.uint32(50).fork()).join();
    }
    if (message.txnAmountRange !== undefined) {
      TxnFilterAmountRange.encode(message.txnAmountRange, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeRange = TxnFilterTimeRange.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.userGroups = UserGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.accountFilters = AccountsFilter.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.groupFilters = RecurrentTxnGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.txnType = TxnFilterTxnType.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.txnCategories.push(TxnFilterCategory.decode(reader, reader.uint32()));
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.txnAmountRange = TxnFilterAmountRange.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupFilters {
    return {
      timeRange: isSet(object.timeRange) ? TxnFilterTimeRange.fromJSON(object.timeRange) : undefined,
      userGroups: isSet(object.userGroups) ? UserGroupFilters.fromJSON(object.userGroups) : undefined,
      accountFilters: isSet(object.accountFilters) ? AccountsFilter.fromJSON(object.accountFilters) : undefined,
      groupFilters: isSet(object.groupFilters) ? RecurrentTxnGroupFilters.fromJSON(object.groupFilters) : undefined,
      txnType: isSet(object.txnType) ? TxnFilterTxnType.fromJSON(object.txnType) : undefined,
      txnCategories: globalThis.Array.isArray(object?.txnCategories)
        ? object.txnCategories.map((e: any) => TxnFilterCategory.fromJSON(e))
        : [],
      txnAmountRange: isSet(object.txnAmountRange) ? TxnFilterAmountRange.fromJSON(object.txnAmountRange) : undefined,
    };
  },

  toJSON(message: RecurrentGroupFilters): unknown {
    const obj: any = {};
    if (message.timeRange !== undefined) {
      obj.timeRange = TxnFilterTimeRange.toJSON(message.timeRange);
    }
    if (message.userGroups !== undefined) {
      obj.userGroups = UserGroupFilters.toJSON(message.userGroups);
    }
    if (message.accountFilters !== undefined) {
      obj.accountFilters = AccountsFilter.toJSON(message.accountFilters);
    }
    if (message.groupFilters !== undefined) {
      obj.groupFilters = RecurrentTxnGroupFilters.toJSON(message.groupFilters);
    }
    if (message.txnType !== undefined) {
      obj.txnType = TxnFilterTxnType.toJSON(message.txnType);
    }
    if (message.txnCategories?.length) {
      obj.txnCategories = message.txnCategories.map((e) => TxnFilterCategory.toJSON(e));
    }
    if (message.txnAmountRange !== undefined) {
      obj.txnAmountRange = TxnFilterAmountRange.toJSON(message.txnAmountRange);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupFilters>, I>>(base?: I): RecurrentGroupFilters {
    return RecurrentGroupFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupFilters>, I>>(object: I): RecurrentGroupFilters {
    const message = createBaseRecurrentGroupFilters();
    message.timeRange = (object.timeRange !== undefined && object.timeRange !== null)
      ? TxnFilterTimeRange.fromPartial(object.timeRange)
      : undefined;
    message.userGroups = (object.userGroups !== undefined && object.userGroups !== null)
      ? UserGroupFilters.fromPartial(object.userGroups)
      : undefined;
    message.accountFilters = (object.accountFilters !== undefined && object.accountFilters !== null)
      ? AccountsFilter.fromPartial(object.accountFilters)
      : undefined;
    message.groupFilters = (object.groupFilters !== undefined && object.groupFilters !== null)
      ? RecurrentTxnGroupFilters.fromPartial(object.groupFilters)
      : undefined;
    message.txnType = (object.txnType !== undefined && object.txnType !== null)
      ? TxnFilterTxnType.fromPartial(object.txnType)
      : undefined;
    message.txnCategories = object.txnCategories?.map((e) => TxnFilterCategory.fromPartial(e)) || [];
    message.txnAmountRange = (object.txnAmountRange !== undefined && object.txnAmountRange !== null)
      ? TxnFilterAmountRange.fromPartial(object.txnAmountRange)
      : undefined;
    return message;
  },
};

function createBaseRecurrentGroupSummary(): RecurrentGroupSummary {
  return {
    recurrentGroupId: "",
    merchant: undefined,
    tag: undefined,
    accountId: "",
    userId: "",
    amount: 0,
    frequency: "",
    dates: [],
    favorite: false,
    excludeCashFlow: false,
    txnMode: "",
  };
}

export const RecurrentGroupSummary: MessageFns<RecurrentGroupSummary> = {
  encode(message: RecurrentGroupSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentGroupId !== "") {
      writer.uint32(10).string(message.recurrentGroupId);
    }
    if (message.merchant !== undefined) {
      Merchant.encode(message.merchant, writer.uint32(18).fork()).join();
    }
    if (message.tag !== undefined) {
      Tag.encode(message.tag, writer.uint32(26).fork()).join();
    }
    if (message.accountId !== "") {
      writer.uint32(34).string(message.accountId);
    }
    if (message.userId !== "") {
      writer.uint32(42).string(message.userId);
    }
    if (message.amount !== 0) {
      writer.uint32(49).double(message.amount);
    }
    if (message.frequency !== "") {
      writer.uint32(58).string(message.frequency);
    }
    for (const v of message.dates) {
      writer.uint32(66).string(v!);
    }
    if (message.favorite !== false) {
      writer.uint32(72).bool(message.favorite);
    }
    if (message.excludeCashFlow !== false) {
      writer.uint32(80).bool(message.excludeCashFlow);
    }
    if (message.txnMode !== "") {
      writer.uint32(90).string(message.txnMode);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.merchant = Merchant.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.tag = Tag.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.accountId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.userId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 49) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.frequency = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.dates.push(reader.string());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.txnMode = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupSummary {
    return {
      recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "",
      merchant: isSet(object.merchant) ? Merchant.fromJSON(object.merchant) : undefined,
      tag: isSet(object.tag) ? Tag.fromJSON(object.tag) : undefined,
      accountId: isSet(object.accountId) ? globalThis.String(object.accountId) : "",
      userId: isSet(object.userId) ? globalThis.String(object.userId) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      frequency: isSet(object.frequency) ? globalThis.String(object.frequency) : "",
      dates: globalThis.Array.isArray(object?.dates) ? object.dates.map((e: any) => globalThis.String(e)) : [],
      favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false,
      excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false,
      txnMode: isSet(object.txnMode) ? globalThis.String(object.txnMode) : "",
    };
  },

  toJSON(message: RecurrentGroupSummary): unknown {
    const obj: any = {};
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    if (message.merchant !== undefined) {
      obj.merchant = Merchant.toJSON(message.merchant);
    }
    if (message.tag !== undefined) {
      obj.tag = Tag.toJSON(message.tag);
    }
    if (message.accountId !== "") {
      obj.accountId = message.accountId;
    }
    if (message.userId !== "") {
      obj.userId = message.userId;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.frequency !== "") {
      obj.frequency = message.frequency;
    }
    if (message.dates?.length) {
      obj.dates = message.dates;
    }
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    if (message.txnMode !== "") {
      obj.txnMode = message.txnMode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupSummary>, I>>(base?: I): RecurrentGroupSummary {
    return RecurrentGroupSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupSummary>, I>>(object: I): RecurrentGroupSummary {
    const message = createBaseRecurrentGroupSummary();
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    message.merchant = (object.merchant !== undefined && object.merchant !== null)
      ? Merchant.fromPartial(object.merchant)
      : undefined;
    message.tag = (object.tag !== undefined && object.tag !== null) ? Tag.fromPartial(object.tag) : undefined;
    message.accountId = object.accountId ?? "";
    message.userId = object.userId ?? "";
    message.amount = object.amount ?? 0;
    message.frequency = object.frequency ?? "";
    message.dates = object.dates?.map((e) => e) || [];
    message.favorite = object.favorite ?? false;
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    message.txnMode = object.txnMode ?? "";
    return message;
  },
};

function createBaseCommonRecurrentGroupCard(): CommonRecurrentGroupCard {
  return { merchant: undefined, amount: 0, userIds: [], frequency: "", dates: [], summaries: [] };
}

export const CommonRecurrentGroupCard: MessageFns<CommonRecurrentGroupCard> = {
  encode(message: CommonRecurrentGroupCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.merchant !== undefined) {
      Merchant.encode(message.merchant, writer.uint32(10).fork()).join();
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    for (const v of message.userIds) {
      writer.uint32(26).string(v!);
    }
    if (message.frequency !== "") {
      writer.uint32(34).string(message.frequency);
    }
    for (const v of message.dates) {
      writer.uint32(42).string(v!);
    }
    for (const v of message.summaries) {
      RecurrentGroupSummary.encode(v!, writer.uint32(50).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CommonRecurrentGroupCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommonRecurrentGroupCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchant = Merchant.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.userIds.push(reader.string());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.frequency = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.dates.push(reader.string());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.summaries.push(RecurrentGroupSummary.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommonRecurrentGroupCard {
    return {
      merchant: isSet(object.merchant) ? Merchant.fromJSON(object.merchant) : undefined,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      userIds: globalThis.Array.isArray(object?.userIds) ? object.userIds.map((e: any) => globalThis.String(e)) : [],
      frequency: isSet(object.frequency) ? globalThis.String(object.frequency) : "",
      dates: globalThis.Array.isArray(object?.dates) ? object.dates.map((e: any) => globalThis.String(e)) : [],
      summaries: globalThis.Array.isArray(object?.summaries)
        ? object.summaries.map((e: any) => RecurrentGroupSummary.fromJSON(e))
        : [],
    };
  },

  toJSON(message: CommonRecurrentGroupCard): unknown {
    const obj: any = {};
    if (message.merchant !== undefined) {
      obj.merchant = Merchant.toJSON(message.merchant);
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.userIds?.length) {
      obj.userIds = message.userIds;
    }
    if (message.frequency !== "") {
      obj.frequency = message.frequency;
    }
    if (message.dates?.length) {
      obj.dates = message.dates;
    }
    if (message.summaries?.length) {
      obj.summaries = message.summaries.map((e) => RecurrentGroupSummary.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommonRecurrentGroupCard>, I>>(base?: I): CommonRecurrentGroupCard {
    return CommonRecurrentGroupCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommonRecurrentGroupCard>, I>>(object: I): CommonRecurrentGroupCard {
    const message = createBaseCommonRecurrentGroupCard();
    message.merchant = (object.merchant !== undefined && object.merchant !== null)
      ? Merchant.fromPartial(object.merchant)
      : undefined;
    message.amount = object.amount ?? 0;
    message.userIds = object.userIds?.map((e) => e) || [];
    message.frequency = object.frequency ?? "";
    message.dates = object.dates?.map((e) => e) || [];
    message.summaries = object.summaries?.map((e) => RecurrentGroupSummary.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetCommonRecurrentGroupsRequest(): GetCommonRecurrentGroupsRequest {
  return { filters: undefined, sortBy: 0 };
}

export const GetCommonRecurrentGroupsRequest: MessageFns<GetCommonRecurrentGroupsRequest> = {
  encode(message: GetCommonRecurrentGroupsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.filters !== undefined) {
      RecurrentGroupFilters.encode(message.filters, writer.uint32(10).fork()).join();
    }
    if (message.sortBy !== 0) {
      writer.uint32(16).int32(message.sortBy);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCommonRecurrentGroupsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCommonRecurrentGroupsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filters = RecurrentGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sortBy = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCommonRecurrentGroupsRequest {
    return {
      filters: isSet(object.filters) ? RecurrentGroupFilters.fromJSON(object.filters) : undefined,
      sortBy: isSet(object.sortBy) ? sortByFromJSON(object.sortBy) : 0,
    };
  },

  toJSON(message: GetCommonRecurrentGroupsRequest): unknown {
    const obj: any = {};
    if (message.filters !== undefined) {
      obj.filters = RecurrentGroupFilters.toJSON(message.filters);
    }
    if (message.sortBy !== 0) {
      obj.sortBy = sortByToJSON(message.sortBy);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCommonRecurrentGroupsRequest>, I>>(base?: I): GetCommonRecurrentGroupsRequest {
    return GetCommonRecurrentGroupsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCommonRecurrentGroupsRequest>, I>>(
    object: I,
  ): GetCommonRecurrentGroupsRequest {
    const message = createBaseGetCommonRecurrentGroupsRequest();
    message.filters = (object.filters !== undefined && object.filters !== null)
      ? RecurrentGroupFilters.fromPartial(object.filters)
      : undefined;
    message.sortBy = object.sortBy ?? 0;
    return message;
  },
};

function createBaseGetCommonRecurrentGroupsResponse(): GetCommonRecurrentGroupsResponse {
  return { cards: [] };
}

export const GetCommonRecurrentGroupsResponse: MessageFns<GetCommonRecurrentGroupsResponse> = {
  encode(message: GetCommonRecurrentGroupsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.cards) {
      CommonRecurrentGroupCard.encode(v!, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetCommonRecurrentGroupsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetCommonRecurrentGroupsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.cards.push(CommonRecurrentGroupCard.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetCommonRecurrentGroupsResponse {
    return {
      cards: globalThis.Array.isArray(object?.cards)
        ? object.cards.map((e: any) => CommonRecurrentGroupCard.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetCommonRecurrentGroupsResponse): unknown {
    const obj: any = {};
    if (message.cards?.length) {
      obj.cards = message.cards.map((e) => CommonRecurrentGroupCard.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetCommonRecurrentGroupsResponse>, I>>(
    base?: I,
  ): GetCommonRecurrentGroupsResponse {
    return GetCommonRecurrentGroupsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCommonRecurrentGroupsResponse>, I>>(
    object: I,
  ): GetCommonRecurrentGroupsResponse {
    const message = createBaseGetCommonRecurrentGroupsResponse();
    message.cards = object.cards?.map((e) => CommonRecurrentGroupCard.fromPartial(e)) || [];
    return message;
  },
};

function createBaseGetRecurrentTxnsRequest(): GetRecurrentTxnsRequest {
  return { filters: undefined, sortBy: 0 };
}

export const GetRecurrentTxnsRequest: MessageFns<GetRecurrentTxnsRequest> = {
  encode(message: GetRecurrentTxnsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.filters !== undefined) {
      RecurrentGroupFilters.encode(message.filters, writer.uint32(10).fork()).join();
    }
    if (message.sortBy !== 0) {
      writer.uint32(16).int32(message.sortBy);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRecurrentTxnsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecurrentTxnsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.filters = RecurrentGroupFilters.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.sortBy = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecurrentTxnsRequest {
    return {
      filters: isSet(object.filters) ? RecurrentGroupFilters.fromJSON(object.filters) : undefined,
      sortBy: isSet(object.sortBy) ? sortByFromJSON(object.sortBy) : 0,
    };
  },

  toJSON(message: GetRecurrentTxnsRequest): unknown {
    const obj: any = {};
    if (message.filters !== undefined) {
      obj.filters = RecurrentGroupFilters.toJSON(message.filters);
    }
    if (message.sortBy !== 0) {
      obj.sortBy = sortByToJSON(message.sortBy);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRecurrentTxnsRequest>, I>>(base?: I): GetRecurrentTxnsRequest {
    return GetRecurrentTxnsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecurrentTxnsRequest>, I>>(object: I): GetRecurrentTxnsRequest {
    const message = createBaseGetRecurrentTxnsRequest();
    message.filters = (object.filters !== undefined && object.filters !== null)
      ? RecurrentGroupFilters.fromPartial(object.filters)
      : undefined;
    message.sortBy = object.sortBy ?? 0;
    return message;
  },
};

function createBaseGetRecurrentTxnsResponse(): GetRecurrentTxnsResponse {
  return { summaries: [], txns: [] };
}

export const GetRecurrentTxnsResponse: MessageFns<GetRecurrentTxnsResponse> = {
  encode(message: GetRecurrentTxnsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.summaries) {
      RecurrentGroupSummary.encode(v!, writer.uint32(10).fork()).join();
    }
    for (const v of message.txns) {
      RecurrentGroupTxnCard.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRecurrentTxnsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecurrentTxnsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.summaries.push(RecurrentGroupSummary.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.txns.push(RecurrentGroupTxnCard.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecurrentTxnsResponse {
    return {
      summaries: globalThis.Array.isArray(object?.summaries)
        ? object.summaries.map((e: any) => RecurrentGroupSummary.fromJSON(e))
        : [],
      txns: globalThis.Array.isArray(object?.txns)
        ? object.txns.map((e: any) => RecurrentGroupTxnCard.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetRecurrentTxnsResponse): unknown {
    const obj: any = {};
    if (message.summaries?.length) {
      obj.summaries = message.summaries.map((e) => RecurrentGroupSummary.toJSON(e));
    }
    if (message.txns?.length) {
      obj.txns = message.txns.map((e) => RecurrentGroupTxnCard.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRecurrentTxnsResponse>, I>>(base?: I): GetRecurrentTxnsResponse {
    return GetRecurrentTxnsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecurrentTxnsResponse>, I>>(object: I): GetRecurrentTxnsResponse {
    const message = createBaseGetRecurrentTxnsResponse();
    message.summaries = object.summaries?.map((e) => RecurrentGroupSummary.fromPartial(e)) || [];
    message.txns = object.txns?.map((e) => RecurrentGroupTxnCard.fromPartial(e)) || [];
    return message;
  },
};

function createBaseRecurrentGroupTxnCard(): RecurrentGroupTxnCard {
  return { recurrentTxnType: 0, txnCard: undefined };
}

export const RecurrentGroupTxnCard: MessageFns<RecurrentGroupTxnCard> = {
  encode(message: RecurrentGroupTxnCard, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentTxnType !== 0) {
      writer.uint32(8).int32(message.recurrentTxnType);
    }
    if (message.txnCard !== undefined) {
      DepositTransactionCard.encode(message.txnCard, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupTxnCard {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupTxnCard();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.recurrentTxnType = reader.int32() as any;
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.txnCard = DepositTransactionCard.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupTxnCard {
    return {
      recurrentTxnType: isSet(object.recurrentTxnType) ? recurrentTxnTypeFromJSON(object.recurrentTxnType) : 0,
      txnCard: isSet(object.txnCard) ? DepositTransactionCard.fromJSON(object.txnCard) : undefined,
    };
  },

  toJSON(message: RecurrentGroupTxnCard): unknown {
    const obj: any = {};
    if (message.recurrentTxnType !== 0) {
      obj.recurrentTxnType = recurrentTxnTypeToJSON(message.recurrentTxnType);
    }
    if (message.txnCard !== undefined) {
      obj.txnCard = DepositTransactionCard.toJSON(message.txnCard);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupTxnCard>, I>>(base?: I): RecurrentGroupTxnCard {
    return RecurrentGroupTxnCard.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupTxnCard>, I>>(object: I): RecurrentGroupTxnCard {
    const message = createBaseRecurrentGroupTxnCard();
    message.recurrentTxnType = object.recurrentTxnType ?? 0;
    message.txnCard = (object.txnCard !== undefined && object.txnCard !== null)
      ? DepositTransactionCard.fromPartial(object.txnCard)
      : undefined;
    return message;
  },
};

function createBaseGetRecurrentGroupDetailsRequest(): GetRecurrentGroupDetailsRequest {
  return { recurrentGroupId: "" };
}

export const GetRecurrentGroupDetailsRequest: MessageFns<GetRecurrentGroupDetailsRequest> = {
  encode(message: GetRecurrentGroupDetailsRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentGroupId !== "") {
      writer.uint32(10).string(message.recurrentGroupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRecurrentGroupDetailsRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecurrentGroupDetailsRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecurrentGroupDetailsRequest {
    return { recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "" };
  },

  toJSON(message: GetRecurrentGroupDetailsRequest): unknown {
    const obj: any = {};
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRecurrentGroupDetailsRequest>, I>>(base?: I): GetRecurrentGroupDetailsRequest {
    return GetRecurrentGroupDetailsRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecurrentGroupDetailsRequest>, I>>(
    object: I,
  ): GetRecurrentGroupDetailsRequest {
    const message = createBaseGetRecurrentGroupDetailsRequest();
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    return message;
  },
};

function createBaseGetRecurrentGroupDetailsResponse(): GetRecurrentGroupDetailsResponse {
  return { summary: undefined, txns: [] };
}

export const GetRecurrentGroupDetailsResponse: MessageFns<GetRecurrentGroupDetailsResponse> = {
  encode(message: GetRecurrentGroupDetailsResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.summary !== undefined) {
      RecurrentGroupSummary.encode(message.summary, writer.uint32(10).fork()).join();
    }
    for (const v of message.txns) {
      RecurrentGroupTxnCard.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetRecurrentGroupDetailsResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetRecurrentGroupDetailsResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.summary = RecurrentGroupSummary.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.txns.push(RecurrentGroupTxnCard.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetRecurrentGroupDetailsResponse {
    return {
      summary: isSet(object.summary) ? RecurrentGroupSummary.fromJSON(object.summary) : undefined,
      txns: globalThis.Array.isArray(object?.txns)
        ? object.txns.map((e: any) => RecurrentGroupTxnCard.fromJSON(e))
        : [],
    };
  },

  toJSON(message: GetRecurrentGroupDetailsResponse): unknown {
    const obj: any = {};
    if (message.summary !== undefined) {
      obj.summary = RecurrentGroupSummary.toJSON(message.summary);
    }
    if (message.txns?.length) {
      obj.txns = message.txns.map((e) => RecurrentGroupTxnCard.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetRecurrentGroupDetailsResponse>, I>>(
    base?: I,
  ): GetRecurrentGroupDetailsResponse {
    return GetRecurrentGroupDetailsResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecurrentGroupDetailsResponse>, I>>(
    object: I,
  ): GetRecurrentGroupDetailsResponse {
    const message = createBaseGetRecurrentGroupDetailsResponse();
    message.summary = (object.summary !== undefined && object.summary !== null)
      ? RecurrentGroupSummary.fromPartial(object.summary)
      : undefined;
    message.txns = object.txns?.map((e) => RecurrentGroupTxnCard.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMarkRecurrentGroupFavoriteRequest(): MarkRecurrentGroupFavoriteRequest {
  return { recurrentGroupId: "", favorite: false };
}

export const MarkRecurrentGroupFavoriteRequest: MessageFns<MarkRecurrentGroupFavoriteRequest> = {
  encode(message: MarkRecurrentGroupFavoriteRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentGroupId !== "") {
      writer.uint32(10).string(message.recurrentGroupId);
    }
    if (message.favorite !== false) {
      writer.uint32(16).bool(message.favorite);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MarkRecurrentGroupFavoriteRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMarkRecurrentGroupFavoriteRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MarkRecurrentGroupFavoriteRequest {
    return {
      recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "",
      favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false,
    };
  },

  toJSON(message: MarkRecurrentGroupFavoriteRequest): unknown {
    const obj: any = {};
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MarkRecurrentGroupFavoriteRequest>, I>>(
    base?: I,
  ): MarkRecurrentGroupFavoriteRequest {
    return MarkRecurrentGroupFavoriteRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MarkRecurrentGroupFavoriteRequest>, I>>(
    object: I,
  ): MarkRecurrentGroupFavoriteRequest {
    const message = createBaseMarkRecurrentGroupFavoriteRequest();
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    message.favorite = object.favorite ?? false;
    return message;
  },
};

function createBaseMarkRecurrentGroupFavoriteResponse(): MarkRecurrentGroupFavoriteResponse {
  return {};
}

export const MarkRecurrentGroupFavoriteResponse: MessageFns<MarkRecurrentGroupFavoriteResponse> = {
  encode(_: MarkRecurrentGroupFavoriteResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): MarkRecurrentGroupFavoriteResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMarkRecurrentGroupFavoriteResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): MarkRecurrentGroupFavoriteResponse {
    return {};
  },

  toJSON(_: MarkRecurrentGroupFavoriteResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<MarkRecurrentGroupFavoriteResponse>, I>>(
    base?: I,
  ): MarkRecurrentGroupFavoriteResponse {
    return MarkRecurrentGroupFavoriteResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MarkRecurrentGroupFavoriteResponse>, I>>(
    _: I,
  ): MarkRecurrentGroupFavoriteResponse {
    const message = createBaseMarkRecurrentGroupFavoriteResponse();
    return message;
  },
};

function createBaseExcludeRecurrentGroupFromCashFlowRequest(): ExcludeRecurrentGroupFromCashFlowRequest {
  return { recurrentGroupId: "", excludeFromCashFlow: false };
}

export const ExcludeRecurrentGroupFromCashFlowRequest: MessageFns<ExcludeRecurrentGroupFromCashFlowRequest> = {
  encode(message: ExcludeRecurrentGroupFromCashFlowRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentGroupId !== "") {
      writer.uint32(10).string(message.recurrentGroupId);
    }
    if (message.excludeFromCashFlow !== false) {
      writer.uint32(16).bool(message.excludeFromCashFlow);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExcludeRecurrentGroupFromCashFlowRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExcludeRecurrentGroupFromCashFlowRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.excludeFromCashFlow = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ExcludeRecurrentGroupFromCashFlowRequest {
    return {
      recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "",
      excludeFromCashFlow: isSet(object.excludeFromCashFlow) ? globalThis.Boolean(object.excludeFromCashFlow) : false,
    };
  },

  toJSON(message: ExcludeRecurrentGroupFromCashFlowRequest): unknown {
    const obj: any = {};
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    if (message.excludeFromCashFlow !== false) {
      obj.excludeFromCashFlow = message.excludeFromCashFlow;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ExcludeRecurrentGroupFromCashFlowRequest>, I>>(
    base?: I,
  ): ExcludeRecurrentGroupFromCashFlowRequest {
    return ExcludeRecurrentGroupFromCashFlowRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExcludeRecurrentGroupFromCashFlowRequest>, I>>(
    object: I,
  ): ExcludeRecurrentGroupFromCashFlowRequest {
    const message = createBaseExcludeRecurrentGroupFromCashFlowRequest();
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    message.excludeFromCashFlow = object.excludeFromCashFlow ?? false;
    return message;
  },
};

function createBaseExcludeRecurrentGroupFromCashFlowResponse(): ExcludeRecurrentGroupFromCashFlowResponse {
  return {};
}

export const ExcludeRecurrentGroupFromCashFlowResponse: MessageFns<ExcludeRecurrentGroupFromCashFlowResponse> = {
  encode(_: ExcludeRecurrentGroupFromCashFlowResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): ExcludeRecurrentGroupFromCashFlowResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseExcludeRecurrentGroupFromCashFlowResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): ExcludeRecurrentGroupFromCashFlowResponse {
    return {};
  },

  toJSON(_: ExcludeRecurrentGroupFromCashFlowResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<ExcludeRecurrentGroupFromCashFlowResponse>, I>>(
    base?: I,
  ): ExcludeRecurrentGroupFromCashFlowResponse {
    return ExcludeRecurrentGroupFromCashFlowResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExcludeRecurrentGroupFromCashFlowResponse>, I>>(
    _: I,
  ): ExcludeRecurrentGroupFromCashFlowResponse {
    const message = createBaseExcludeRecurrentGroupFromCashFlowResponse();
    return message;
  },
};

function createBaseDeleteRecurrentGroupRequest(): DeleteRecurrentGroupRequest {
  return { recurrentGroupId: "" };
}

export const DeleteRecurrentGroupRequest: MessageFns<DeleteRecurrentGroupRequest> = {
  encode(message: DeleteRecurrentGroupRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentGroupId !== "") {
      writer.uint32(10).string(message.recurrentGroupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteRecurrentGroupRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteRecurrentGroupRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DeleteRecurrentGroupRequest {
    return { recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "" };
  },

  toJSON(message: DeleteRecurrentGroupRequest): unknown {
    const obj: any = {};
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteRecurrentGroupRequest>, I>>(base?: I): DeleteRecurrentGroupRequest {
    return DeleteRecurrentGroupRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRecurrentGroupRequest>, I>>(object: I): DeleteRecurrentGroupRequest {
    const message = createBaseDeleteRecurrentGroupRequest();
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    return message;
  },
};

function createBaseDeleteRecurrentGroupResponse(): DeleteRecurrentGroupResponse {
  return {};
}

export const DeleteRecurrentGroupResponse: MessageFns<DeleteRecurrentGroupResponse> = {
  encode(_: DeleteRecurrentGroupResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeleteRecurrentGroupResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeleteRecurrentGroupResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeleteRecurrentGroupResponse {
    return {};
  },

  toJSON(_: DeleteRecurrentGroupResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeleteRecurrentGroupResponse>, I>>(base?: I): DeleteRecurrentGroupResponse {
    return DeleteRecurrentGroupResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRecurrentGroupResponse>, I>>(_: I): DeleteRecurrentGroupResponse {
    const message = createBaseDeleteRecurrentGroupResponse();
    return message;
  },
};

function createBaseRemoveTxnFromRecurrentGroupRequest(): RemoveTxnFromRecurrentGroupRequest {
  return { txnId: "", recurrentGroupId: "" };
}

export const RemoveTxnFromRecurrentGroupRequest: MessageFns<RemoveTxnFromRecurrentGroupRequest> = {
  encode(message: RemoveTxnFromRecurrentGroupRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnId !== "") {
      writer.uint32(10).string(message.txnId);
    }
    if (message.recurrentGroupId !== "") {
      writer.uint32(18).string(message.recurrentGroupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RemoveTxnFromRecurrentGroupRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRemoveTxnFromRecurrentGroupRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RemoveTxnFromRecurrentGroupRequest {
    return {
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "",
    };
  },

  toJSON(message: RemoveTxnFromRecurrentGroupRequest): unknown {
    const obj: any = {};
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RemoveTxnFromRecurrentGroupRequest>, I>>(
    base?: I,
  ): RemoveTxnFromRecurrentGroupRequest {
    return RemoveTxnFromRecurrentGroupRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveTxnFromRecurrentGroupRequest>, I>>(
    object: I,
  ): RemoveTxnFromRecurrentGroupRequest {
    const message = createBaseRemoveTxnFromRecurrentGroupRequest();
    message.txnId = object.txnId ?? "";
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    return message;
  },
};

function createBaseRemoveTxnFromRecurrentGroupResponse(): RemoveTxnFromRecurrentGroupResponse {
  return {};
}

export const RemoveTxnFromRecurrentGroupResponse: MessageFns<RemoveTxnFromRecurrentGroupResponse> = {
  encode(_: RemoveTxnFromRecurrentGroupResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RemoveTxnFromRecurrentGroupResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRemoveTxnFromRecurrentGroupResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): RemoveTxnFromRecurrentGroupResponse {
    return {};
  },

  toJSON(_: RemoveTxnFromRecurrentGroupResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<RemoveTxnFromRecurrentGroupResponse>, I>>(
    base?: I,
  ): RemoveTxnFromRecurrentGroupResponse {
    return RemoveTxnFromRecurrentGroupResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveTxnFromRecurrentGroupResponse>, I>>(
    _: I,
  ): RemoveTxnFromRecurrentGroupResponse {
    const message = createBaseRemoveTxnFromRecurrentGroupResponse();
    return message;
  },
};

function createBaseAddTxnToRecurrentGroupRequest(): AddTxnToRecurrentGroupRequest {
  return { txnId: "", recurrentGroupId: "" };
}

export const AddTxnToRecurrentGroupRequest: MessageFns<AddTxnToRecurrentGroupRequest> = {
  encode(message: AddTxnToRecurrentGroupRequest, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnId !== "") {
      writer.uint32(10).string(message.txnId);
    }
    if (message.recurrentGroupId !== "") {
      writer.uint32(18).string(message.recurrentGroupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddTxnToRecurrentGroupRequest {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddTxnToRecurrentGroupRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AddTxnToRecurrentGroupRequest {
    return {
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "",
    };
  },

  toJSON(message: AddTxnToRecurrentGroupRequest): unknown {
    const obj: any = {};
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AddTxnToRecurrentGroupRequest>, I>>(base?: I): AddTxnToRecurrentGroupRequest {
    return AddTxnToRecurrentGroupRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddTxnToRecurrentGroupRequest>, I>>(
    object: I,
  ): AddTxnToRecurrentGroupRequest {
    const message = createBaseAddTxnToRecurrentGroupRequest();
    message.txnId = object.txnId ?? "";
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    return message;
  },
};

function createBaseAddTxnToRecurrentGroupResponse(): AddTxnToRecurrentGroupResponse {
  return {};
}

export const AddTxnToRecurrentGroupResponse: MessageFns<AddTxnToRecurrentGroupResponse> = {
  encode(_: AddTxnToRecurrentGroupResponse, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): AddTxnToRecurrentGroupResponse {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAddTxnToRecurrentGroupResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): AddTxnToRecurrentGroupResponse {
    return {};
  },

  toJSON(_: AddTxnToRecurrentGroupResponse): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<AddTxnToRecurrentGroupResponse>, I>>(base?: I): AddTxnToRecurrentGroupResponse {
    return AddTxnToRecurrentGroupResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddTxnToRecurrentGroupResponse>, I>>(_: I): AddTxnToRecurrentGroupResponse {
    const message = createBaseAddTxnToRecurrentGroupResponse();
    return message;
  },
};

/** Service for Recurrent Groups */
export type RecurrentGroupsService = typeof RecurrentGroupsService;
export const RecurrentGroupsService = {
  getRecurrentTxns: {
    path: "/backend_services.visualization.RecurrentGroups/GetRecurrentTxns",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetRecurrentTxnsRequest) => Buffer.from(GetRecurrentTxnsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetRecurrentTxnsRequest.decode(value),
    responseSerialize: (value: GetRecurrentTxnsResponse) =>
      Buffer.from(GetRecurrentTxnsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetRecurrentTxnsResponse.decode(value),
  },
  getCommonRecurrentGroups: {
    path: "/backend_services.visualization.RecurrentGroups/GetCommonRecurrentGroups",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetCommonRecurrentGroupsRequest) =>
      Buffer.from(GetCommonRecurrentGroupsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetCommonRecurrentGroupsRequest.decode(value),
    responseSerialize: (value: GetCommonRecurrentGroupsResponse) =>
      Buffer.from(GetCommonRecurrentGroupsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetCommonRecurrentGroupsResponse.decode(value),
  },
  getRecurrentGroupDetails: {
    path: "/backend_services.visualization.RecurrentGroups/GetRecurrentGroupDetails",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: GetRecurrentGroupDetailsRequest) =>
      Buffer.from(GetRecurrentGroupDetailsRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => GetRecurrentGroupDetailsRequest.decode(value),
    responseSerialize: (value: GetRecurrentGroupDetailsResponse) =>
      Buffer.from(GetRecurrentGroupDetailsResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => GetRecurrentGroupDetailsResponse.decode(value),
  },
  markRecurrentGroupFavorite: {
    path: "/backend_services.visualization.RecurrentGroups/MarkRecurrentGroupFavorite",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: MarkRecurrentGroupFavoriteRequest) =>
      Buffer.from(MarkRecurrentGroupFavoriteRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => MarkRecurrentGroupFavoriteRequest.decode(value),
    responseSerialize: (value: MarkRecurrentGroupFavoriteResponse) =>
      Buffer.from(MarkRecurrentGroupFavoriteResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => MarkRecurrentGroupFavoriteResponse.decode(value),
  },
  excludeRecurrentGroupFromCashFlow: {
    path: "/backend_services.visualization.RecurrentGroups/ExcludeRecurrentGroupFromCashFlow",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: ExcludeRecurrentGroupFromCashFlowRequest) =>
      Buffer.from(ExcludeRecurrentGroupFromCashFlowRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => ExcludeRecurrentGroupFromCashFlowRequest.decode(value),
    responseSerialize: (value: ExcludeRecurrentGroupFromCashFlowResponse) =>
      Buffer.from(ExcludeRecurrentGroupFromCashFlowResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => ExcludeRecurrentGroupFromCashFlowResponse.decode(value),
  },
  deleteRecurrentGroup: {
    path: "/backend_services.visualization.RecurrentGroups/DeleteRecurrentGroup",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: DeleteRecurrentGroupRequest) =>
      Buffer.from(DeleteRecurrentGroupRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => DeleteRecurrentGroupRequest.decode(value),
    responseSerialize: (value: DeleteRecurrentGroupResponse) =>
      Buffer.from(DeleteRecurrentGroupResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => DeleteRecurrentGroupResponse.decode(value),
  },
  addTxnToRecurrentGroup: {
    path: "/backend_services.visualization.RecurrentGroups/AddTxnToRecurrentGroup",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: AddTxnToRecurrentGroupRequest) =>
      Buffer.from(AddTxnToRecurrentGroupRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => AddTxnToRecurrentGroupRequest.decode(value),
    responseSerialize: (value: AddTxnToRecurrentGroupResponse) =>
      Buffer.from(AddTxnToRecurrentGroupResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => AddTxnToRecurrentGroupResponse.decode(value),
  },
  removeTxnFromRecurrentGroup: {
    path: "/backend_services.visualization.RecurrentGroups/RemoveTxnFromRecurrentGroup",
    requestStream: false,
    responseStream: false,
    requestSerialize: (value: RemoveTxnFromRecurrentGroupRequest) =>
      Buffer.from(RemoveTxnFromRecurrentGroupRequest.encode(value).finish()),
    requestDeserialize: (value: Buffer) => RemoveTxnFromRecurrentGroupRequest.decode(value),
    responseSerialize: (value: RemoveTxnFromRecurrentGroupResponse) =>
      Buffer.from(RemoveTxnFromRecurrentGroupResponse.encode(value).finish()),
    responseDeserialize: (value: Buffer) => RemoveTxnFromRecurrentGroupResponse.decode(value),
  },
} as const;

export interface RecurrentGroupsServer extends UntypedServiceImplementation {
  getRecurrentTxns: handleUnaryCall<GetRecurrentTxnsRequest, GetRecurrentTxnsResponse>;
  getCommonRecurrentGroups: handleUnaryCall<GetCommonRecurrentGroupsRequest, GetCommonRecurrentGroupsResponse>;
  getRecurrentGroupDetails: handleUnaryCall<GetRecurrentGroupDetailsRequest, GetRecurrentGroupDetailsResponse>;
  markRecurrentGroupFavorite: handleUnaryCall<MarkRecurrentGroupFavoriteRequest, MarkRecurrentGroupFavoriteResponse>;
  excludeRecurrentGroupFromCashFlow: handleUnaryCall<
    ExcludeRecurrentGroupFromCashFlowRequest,
    ExcludeRecurrentGroupFromCashFlowResponse
  >;
  deleteRecurrentGroup: handleUnaryCall<DeleteRecurrentGroupRequest, DeleteRecurrentGroupResponse>;
  addTxnToRecurrentGroup: handleUnaryCall<AddTxnToRecurrentGroupRequest, AddTxnToRecurrentGroupResponse>;
  removeTxnFromRecurrentGroup: handleUnaryCall<RemoveTxnFromRecurrentGroupRequest, RemoveTxnFromRecurrentGroupResponse>;
}

export interface RecurrentGroupsClient extends Client {
  getRecurrentTxns(
    request: GetRecurrentTxnsRequest,
    callback: (error: ServiceError | null, response: GetRecurrentTxnsResponse) => void,
  ): ClientUnaryCall;
  getRecurrentTxns(
    request: GetRecurrentTxnsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetRecurrentTxnsResponse) => void,
  ): ClientUnaryCall;
  getRecurrentTxns(
    request: GetRecurrentTxnsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetRecurrentTxnsResponse) => void,
  ): ClientUnaryCall;
  getCommonRecurrentGroups(
    request: GetCommonRecurrentGroupsRequest,
    callback: (error: ServiceError | null, response: GetCommonRecurrentGroupsResponse) => void,
  ): ClientUnaryCall;
  getCommonRecurrentGroups(
    request: GetCommonRecurrentGroupsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetCommonRecurrentGroupsResponse) => void,
  ): ClientUnaryCall;
  getCommonRecurrentGroups(
    request: GetCommonRecurrentGroupsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetCommonRecurrentGroupsResponse) => void,
  ): ClientUnaryCall;
  getRecurrentGroupDetails(
    request: GetRecurrentGroupDetailsRequest,
    callback: (error: ServiceError | null, response: GetRecurrentGroupDetailsResponse) => void,
  ): ClientUnaryCall;
  getRecurrentGroupDetails(
    request: GetRecurrentGroupDetailsRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: GetRecurrentGroupDetailsResponse) => void,
  ): ClientUnaryCall;
  getRecurrentGroupDetails(
    request: GetRecurrentGroupDetailsRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: GetRecurrentGroupDetailsResponse) => void,
  ): ClientUnaryCall;
  markRecurrentGroupFavorite(
    request: MarkRecurrentGroupFavoriteRequest,
    callback: (error: ServiceError | null, response: MarkRecurrentGroupFavoriteResponse) => void,
  ): ClientUnaryCall;
  markRecurrentGroupFavorite(
    request: MarkRecurrentGroupFavoriteRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: MarkRecurrentGroupFavoriteResponse) => void,
  ): ClientUnaryCall;
  markRecurrentGroupFavorite(
    request: MarkRecurrentGroupFavoriteRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: MarkRecurrentGroupFavoriteResponse) => void,
  ): ClientUnaryCall;
  excludeRecurrentGroupFromCashFlow(
    request: ExcludeRecurrentGroupFromCashFlowRequest,
    callback: (error: ServiceError | null, response: ExcludeRecurrentGroupFromCashFlowResponse) => void,
  ): ClientUnaryCall;
  excludeRecurrentGroupFromCashFlow(
    request: ExcludeRecurrentGroupFromCashFlowRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: ExcludeRecurrentGroupFromCashFlowResponse) => void,
  ): ClientUnaryCall;
  excludeRecurrentGroupFromCashFlow(
    request: ExcludeRecurrentGroupFromCashFlowRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: ExcludeRecurrentGroupFromCashFlowResponse) => void,
  ): ClientUnaryCall;
  deleteRecurrentGroup(
    request: DeleteRecurrentGroupRequest,
    callback: (error: ServiceError | null, response: DeleteRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  deleteRecurrentGroup(
    request: DeleteRecurrentGroupRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: DeleteRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  deleteRecurrentGroup(
    request: DeleteRecurrentGroupRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: DeleteRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  addTxnToRecurrentGroup(
    request: AddTxnToRecurrentGroupRequest,
    callback: (error: ServiceError | null, response: AddTxnToRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  addTxnToRecurrentGroup(
    request: AddTxnToRecurrentGroupRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: AddTxnToRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  addTxnToRecurrentGroup(
    request: AddTxnToRecurrentGroupRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: AddTxnToRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  removeTxnFromRecurrentGroup(
    request: RemoveTxnFromRecurrentGroupRequest,
    callback: (error: ServiceError | null, response: RemoveTxnFromRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  removeTxnFromRecurrentGroup(
    request: RemoveTxnFromRecurrentGroupRequest,
    metadata: Metadata,
    callback: (error: ServiceError | null, response: RemoveTxnFromRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
  removeTxnFromRecurrentGroup(
    request: RemoveTxnFromRecurrentGroupRequest,
    metadata: Metadata,
    options: Partial<CallOptions>,
    callback: (error: ServiceError | null, response: RemoveTxnFromRecurrentGroupResponse) => void,
  ): ClientUnaryCall;
}

export const RecurrentGroupsClient = makeGenericClientConstructor(
  RecurrentGroupsService,
  "backend_services.visualization.RecurrentGroups",
) as unknown as {
  new (address: string, credentials: ChannelCredentials, options?: Partial<ClientOptions>): RecurrentGroupsClient;
  service: typeof RecurrentGroupsService;
  serviceName: string;
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
