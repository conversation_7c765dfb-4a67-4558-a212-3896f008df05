// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.31.1
// source: database/fi_deposit.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";
import { Profile, Summary, Transaction } from "../rebit/fi_schemas/deposit/deposit";
import {
  CalendarPeriod,
  calendarPeriodFromJSON,
  calendarPeriodToJSON,
  DateMessage,
  DayOfWeek,
  dayOfWeekFromJSON,
  dayOfWeekToJSON,
} from "../shared/shared";
import { ObjectId } from "./custom_type";

export const protobufPackage = "database";

export enum DepositTxnCategoryUpdateSource {
  NONE = 0,
  MONGO_KEYWORD = 1,
  LLM = 2,
  USER = 3,
  USER_PERSONALISED = 4,
  UNRECOGNIZED = -1,
}

export function depositTxnCategoryUpdateSourceFromJSON(object: any): DepositTxnCategoryUpdateSource {
  switch (object) {
    case 0:
    case "NONE":
      return DepositTxnCategoryUpdateSource.NONE;
    case 1:
    case "MONGO_KEYWORD":
      return DepositTxnCategoryUpdateSource.MONGO_KEYWORD;
    case 2:
    case "LLM":
      return DepositTxnCategoryUpdateSource.LLM;
    case 3:
    case "USER":
      return DepositTxnCategoryUpdateSource.USER;
    case 4:
    case "USER_PERSONALISED":
      return DepositTxnCategoryUpdateSource.USER_PERSONALISED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DepositTxnCategoryUpdateSource.UNRECOGNIZED;
  }
}

export function depositTxnCategoryUpdateSourceToJSON(object: DepositTxnCategoryUpdateSource): string {
  switch (object) {
    case DepositTxnCategoryUpdateSource.NONE:
      return "NONE";
    case DepositTxnCategoryUpdateSource.MONGO_KEYWORD:
      return "MONGO_KEYWORD";
    case DepositTxnCategoryUpdateSource.LLM:
      return "LLM";
    case DepositTxnCategoryUpdateSource.USER:
      return "USER";
    case DepositTxnCategoryUpdateSource.USER_PERSONALISED:
      return "USER_PERSONALISED";
    case DepositTxnCategoryUpdateSource.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** flag:is-collection=true; */
export interface FiDepositAccountSummary {
  Id:
    | ObjectId
    | undefined;
  /** FIP Identifier */
  fipId: string;
  /** Financial information type */
  fiType: string;
  /** Masked account number */
  maskedAccNumber: string;
  /** Unique account number associated with aa account. */
  linkedAccRef: string;
  /** Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type. */
  profile:
    | Profile
    | undefined;
  /** The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account. */
  summary:
    | Summary
    | undefined;
  /** Document creation timestamp */
  createdAt: number;
  /** Document modification timestamp */
  updatedAt: number;
  initialSummary: FiDepositAccountInitialSummary | undefined;
}

export interface FiDepositAccountInitialSummary {
  summary:
    | Summary
    | undefined;
  /** @gotags: bson:"last_transaction_timestamp" */
  lastTransactionTimestamp: number;
}

/** flag:is-collection=true; */
export interface FiDepositAccountTransaction {
  Id:
    | ObjectId
    | undefined;
  /** FIP Identifier */
  fipId: string;
  /** Financial information type */
  fiType: string;
  /** _id of account summary record in database */
  accountId:
    | ObjectId
    | undefined;
  /** Masked account number */
  maskedAccNumber: string;
  /** Details of all transactions that have been posted in an account. */
  transaction:
    | Transaction
    | undefined;
  /** Document creation timestamp */
  createdAt: number;
  /** Document modification timestamp */
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface DepositAccountDailyClosingBalance {
  Id: ObjectId | undefined;
  accountId: ObjectId | undefined;
  summaryDate: number;
  closingBalance: string;
  /** @gotags: bson:"last_processed_transaction_time" */
  lastProcessedTransactionTime: number;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface DepositTxnCategoryMetadata {
  /** Value of _id field is not autogenerated, but is populated to be same as value of _id field of corresponding doc from FiDepositAccountTransaction collection. */
  Id: ObjectId | undefined;
  accountId:
    | ObjectId
    | undefined;
  /** Tagging state */
  status: string;
  /** Remark */
  transactionNarration: string;
  /** _id of the category doc that this transaction belongs to */
  autoTransactionCategoryId: string;
  /** Who */
  entity: string[];
  /** Why */
  purpose: string[];
  /** IFSC Code of entity, if available */
  ifsc: string;
  /** Additional notes generated by tagging */
  notes: string;
  /** Pattern found in tagging */
  matchedPattern: string;
  /** Retool processing status */
  annotationToolStatus: string;
  createdAt: number;
  updatedAt: number;
}

export interface CashFlowPeriod {
  month: number;
  year: number;
}

export interface TxnFilters {
  timeRange: TxnFilterTimeRange | undefined;
  category: TxnFilterCategory[];
  amountRange: TxnFilterAmountRange | undefined;
  merchant: TxnFilterMerchant[];
  narration: TxnFilterNarration | undefined;
  favorited: TxnFilterFavorited | undefined;
  untagged: TxnFilterUntagged | undefined;
  hasUserNotes: TxnFilterHasUserNotes | undefined;
  excludeCashFlow: TxnFilterExcludeCashFlow | undefined;
  txnType: TxnFilterTxnType | undefined;
  hasRecurrentGroup: TxnFilterHasRecurrentGroup | undefined;
  notes: TxnFilterNotes | undefined;
  recurrentGroups: TxnFilterRecurrentGroup[];
}

export interface TxnFilterTimeRange {
  fromTime: number;
  toTime: number;
}

export interface TxnFilterCategory {
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}

export interface TxnFilterAmountRange {
  minAmount: number;
  maxAmount: number;
  /** Whether to include this filter in search */
  includeForSearch: boolean;
}

export interface TxnFilterMerchant {
  merchantId: string;
}

export interface TxnFilterNarration {
  narration: string;
}

export interface TxnFilterTxnType {
  txnType: string;
}

export interface TxnFilterFavorited {
  favorited: boolean;
}

export interface TxnFilterUntagged {
  untagged: boolean;
}

export interface TxnFilterHasUserNotes {
  hasUserNotes: boolean;
}

export interface TxnFilterExcludeCashFlow {
  excludeCashFlow: boolean;
}

export interface TxnPaginationParams {
  pageSize: number;
  pageNumber: number;
}

export interface TxnFilterHasRecurrentGroup {
  hasRecurrentGroup: boolean;
}

export interface TxnFilterRecurrentGroup {
  recurrentGroupId: string;
}

export interface TxnFilterNotes {
  notes: string;
}

/**
 * flag:is-collection=true;
 * TODO: Have taken it from: https://github.com/cusp-money/protos/pull/109/files
 */
export interface DepositTransaction {
  /** auto-generated */
  Id:
    | ObjectId
    | undefined;
  /** same as _id field of corresponding doc from FiDepositAccountTransaction */
  txnId:
    | ObjectId
    | undefined;
  /** when the record was updated */
  timestamp: number;
  /** who created this update */
  updateSource: DepositTransaction_UpdateSource;
  /** raw transaction */
  rawTransaction:
    | FiDepositAccountTransaction
    | undefined;
  /** Tagging status */
  status: string;
  /** Parsed Who */
  entity: string[];
  /** Parsed Why */
  purpose: string[];
  /** IFSC Code of parsed entity, if available */
  ifsc: string;
  /** Notes provided by user */
  userNotes: string;
  /** Pattern found in parsing */
  matchedPattern: string;
  /** Txn category id */
  categoryId: string;
  /** Txn subcategory id */
  subcategoryId: string;
  /** global or <uid> */
  categoryCollection: string;
  /** Source that updated fields `category_id`` and `subcategory_id` */
  categoryUpdateSource: DepositTxnCategoryUpdateSource;
  /** Timestamp at which fields category_id field was updated */
  categoryIdUpdatedAt: number;
  /** Txn merchant id */
  merchantId: string;
  /** Exclude txn from all cash flow analysis */
  excludeCashFlow: boolean;
  /** User has favorited/bookmarked transaction */
  favorite: boolean;
  createdAt: number;
  /** Reason of current tagging prediction. Generated by tagging service. */
  predictionReason: string;
  /** Txn accounted in which period in cashflow */
  cashflowAccountInPeriod:
    | CashFlowPeriod
    | undefined;
  /** User attached documents relevant for this transaction */
  documents: DepositTransaction_Document[];
  /** Id of recurrent transaction group this belongs to */
  recurrentGroupId: ObjectId | undefined;
  identifierKeywords: string[];
}

export enum DepositTransaction_UpdateSource {
  NONE = 0,
  SYSTEM = 1,
  USER = 2,
  UNRECOGNIZED = -1,
}

export function depositTransaction_UpdateSourceFromJSON(object: any): DepositTransaction_UpdateSource {
  switch (object) {
    case 0:
    case "NONE":
      return DepositTransaction_UpdateSource.NONE;
    case 1:
    case "SYSTEM":
      return DepositTransaction_UpdateSource.SYSTEM;
    case 2:
    case "USER":
      return DepositTransaction_UpdateSource.USER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return DepositTransaction_UpdateSource.UNRECOGNIZED;
  }
}

export function depositTransaction_UpdateSourceToJSON(object: DepositTransaction_UpdateSource): string {
  switch (object) {
    case DepositTransaction_UpdateSource.NONE:
      return "NONE";
    case DepositTransaction_UpdateSource.SYSTEM:
      return "SYSTEM";
    case DepositTransaction_UpdateSource.USER:
      return "USER";
    case DepositTransaction_UpdateSource.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface DepositTransaction_Document {
  /** Mirror of cloud storage object name */
  objectName: string;
  /** file name */
  fileName: string;
  /** Bucket where file is stored */
  bucket: string;
  /** Base64 encoded thumbnail for images */
  thumbnail: string;
  /** MIME type of the document */
  contentType: string;
}

/** flag:is-collection=true; */
export interface RecurrentTransactionGroup {
  /** auto-generated */
  Id:
    | ObjectId
    | undefined;
  /** One group per account */
  accountId:
    | ObjectId
    | undefined;
  /** Subtag */
  subcategoryId:
    | ObjectId
    | undefined;
  /** Tag */
  categoryId:
    | ObjectId
    | undefined;
  /** "CREDIT" or "DEBIT" */
  txnType: string;
  /** Most recent/frequent payment mode e.g. Auto Debit, UPI, NEFT */
  txnMode: string;
  /** Start time of recurrent group */
  startTime:
    | RecurrentGroupStartTime
    | undefined;
  /** Frequency of recurrence */
  frequency:
    | RecurrentGroupFrequency
    | undefined;
  /** Most recent/frequent merchant_id of the group */
  merchantId:
    | ObjectId
    | undefined;
  /** Group saved by user */
  favorite: boolean;
  /** All txns of group excluded from cash flow */
  excludeCashFlow: boolean;
  /** The group has been marked as completed (e.g. all installments of EMI paid) */
  completed: boolean;
  /** Notes provided by user */
  userNotes: string;
  /** Auto-derived or user-provided date, by which the group would complete */
  endDate: number;
  createdAt: number;
  updatedAt: number;
}

export interface RecurrentGroupStartTime {
  /**
   * Month of Year (e.g. January) - Every quarter, with quarter starting at January
   * Day of Month (e.g. 12th) - Every month, with month starting at 12th
   */
  date:
    | DateMessage
    | undefined;
  /** Day of Week (e.g. Monday) - Every Monday */
  dayOfWeek: DayOfWeek;
}

export interface RecurrentGroupFrequency {
  /** E.g. (repeats:2 period:month) - Txn repeats every 2 months */
  repeats: number;
  period: CalendarPeriod;
}

export interface RecurrentTxnGroupFilters {
  frequency: RecurrentGroupFrequency[];
  favorite: RecurrentGroupFilterFavorite | undefined;
  excludeCashFlow: RecurrentGroupFilterExcludeCashFlow | undefined;
}

export interface RecurrentGroupFilterFavorite {
  favorite: boolean;
}

export interface RecurrentGroupFilterExcludeCashFlow {
  excludeCashFlow: boolean;
}

/** flag:is-collection=true; */
export interface DeletedFiDepositAccountSummary {
}

/** flag:is-collection=true; */
export interface DeletedFiDepositAccountTransaction {
}

/** flag:is-collection=true; */
export interface DeletedFiDepositTxnCategoryMetadata {
}

/** flag:is-collection=true; */
export interface DepositTxnUserPersonalizedTagFreq {
  /** auto-generated */
  Id: ObjectId | undefined;
  keyword: string;
  categoryId: string;
  subcategoryId: string;
  frequency: number;
  /** credit or debit */
  type: string;
  /** system user id */
  userId:
    | ObjectId
    | undefined;
  /** family id */
  familyId: string;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface DepositTxnLlmPredictedTagFreq {
  /** auto-generated */
  Id: ObjectId | undefined;
  keyword: string;
  categoryId: string;
  subcategoryId: string;
  /** credit or debit */
  type: string;
  frequency: number;
  createdAt: number;
  updatedAt: number;
}

/** flag:is-collection=true; */
export interface UntaggedDepositTxnAnnotatorReview {
  /** auto-generated */
  Id: ObjectId | undefined;
  keyword: string;
  /** credit or debit */
  type: string;
  txnNarrations: string[];
  txnCount: number;
  taggedTxnWhoseTagsWillChange: number;
  isProcessedByAnnotator: boolean;
  isProcessedByReviewer: boolean;
  createdAt: number;
  updatedAt: number;
}

function createBaseFiDepositAccountSummary(): FiDepositAccountSummary {
  return {
    Id: undefined,
    fipId: "",
    fiType: "",
    maskedAccNumber: "",
    linkedAccRef: "",
    profile: undefined,
    summary: undefined,
    createdAt: 0,
    updatedAt: 0,
    initialSummary: undefined,
  };
}

export const FiDepositAccountSummary: MessageFns<FiDepositAccountSummary> = {
  encode(message: FiDepositAccountSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.fipId !== "") {
      writer.uint32(18).string(message.fipId);
    }
    if (message.fiType !== "") {
      writer.uint32(26).string(message.fiType);
    }
    if (message.maskedAccNumber !== "") {
      writer.uint32(42).string(message.maskedAccNumber);
    }
    if (message.linkedAccRef !== "") {
      writer.uint32(50).string(message.linkedAccRef);
    }
    if (message.profile !== undefined) {
      Profile.encode(message.profile, writer.uint32(58).fork()).join();
    }
    if (message.summary !== undefined) {
      Summary.encode(message.summary, writer.uint32(66).fork()).join();
    }
    if (message.createdAt !== 0) {
      writer.uint32(72).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(80).int64(message.updatedAt);
    }
    if (message.initialSummary !== undefined) {
      FiDepositAccountInitialSummary.encode(message.initialSummary, writer.uint32(90).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FiDepositAccountSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFiDepositAccountSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fiType = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.maskedAccNumber = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.linkedAccRef = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.profile = Profile.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.summary = Summary.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 10: {
          if (tag !== 80) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.initialSummary = FiDepositAccountInitialSummary.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FiDepositAccountSummary {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      fiType: isSet(object.fiType) ? globalThis.String(object.fiType) : "",
      maskedAccNumber: isSet(object.maskedAccNumber) ? globalThis.String(object.maskedAccNumber) : "",
      linkedAccRef: isSet(object.linkedAccRef) ? globalThis.String(object.linkedAccRef) : "",
      profile: isSet(object.profile) ? Profile.fromJSON(object.profile) : undefined,
      summary: isSet(object.summary) ? Summary.fromJSON(object.summary) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
      initialSummary: isSet(object.initialSummary)
        ? FiDepositAccountInitialSummary.fromJSON(object.initialSummary)
        : undefined,
    };
  },

  toJSON(message: FiDepositAccountSummary): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.fiType !== "") {
      obj.fiType = message.fiType;
    }
    if (message.maskedAccNumber !== "") {
      obj.maskedAccNumber = message.maskedAccNumber;
    }
    if (message.linkedAccRef !== "") {
      obj.linkedAccRef = message.linkedAccRef;
    }
    if (message.profile !== undefined) {
      obj.profile = Profile.toJSON(message.profile);
    }
    if (message.summary !== undefined) {
      obj.summary = Summary.toJSON(message.summary);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    if (message.initialSummary !== undefined) {
      obj.initialSummary = FiDepositAccountInitialSummary.toJSON(message.initialSummary);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FiDepositAccountSummary>, I>>(base?: I): FiDepositAccountSummary {
    return FiDepositAccountSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FiDepositAccountSummary>, I>>(object: I): FiDepositAccountSummary {
    const message = createBaseFiDepositAccountSummary();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.fipId = object.fipId ?? "";
    message.fiType = object.fiType ?? "";
    message.maskedAccNumber = object.maskedAccNumber ?? "";
    message.linkedAccRef = object.linkedAccRef ?? "";
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? Profile.fromPartial(object.profile)
      : undefined;
    message.summary = (object.summary !== undefined && object.summary !== null)
      ? Summary.fromPartial(object.summary)
      : undefined;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    message.initialSummary = (object.initialSummary !== undefined && object.initialSummary !== null)
      ? FiDepositAccountInitialSummary.fromPartial(object.initialSummary)
      : undefined;
    return message;
  },
};

function createBaseFiDepositAccountInitialSummary(): FiDepositAccountInitialSummary {
  return { summary: undefined, lastTransactionTimestamp: 0 };
}

export const FiDepositAccountInitialSummary: MessageFns<FiDepositAccountInitialSummary> = {
  encode(message: FiDepositAccountInitialSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.summary !== undefined) {
      Summary.encode(message.summary, writer.uint32(10).fork()).join();
    }
    if (message.lastTransactionTimestamp !== 0) {
      writer.uint32(16).int64(message.lastTransactionTimestamp);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FiDepositAccountInitialSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFiDepositAccountInitialSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.summary = Summary.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.lastTransactionTimestamp = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FiDepositAccountInitialSummary {
    return {
      summary: isSet(object.summary) ? Summary.fromJSON(object.summary) : undefined,
      lastTransactionTimestamp: isSet(object.lastTransactionTimestamp)
        ? globalThis.Number(object.lastTransactionTimestamp)
        : 0,
    };
  },

  toJSON(message: FiDepositAccountInitialSummary): unknown {
    const obj: any = {};
    if (message.summary !== undefined) {
      obj.summary = Summary.toJSON(message.summary);
    }
    if (message.lastTransactionTimestamp !== 0) {
      obj.lastTransactionTimestamp = Math.round(message.lastTransactionTimestamp);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FiDepositAccountInitialSummary>, I>>(base?: I): FiDepositAccountInitialSummary {
    return FiDepositAccountInitialSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FiDepositAccountInitialSummary>, I>>(
    object: I,
  ): FiDepositAccountInitialSummary {
    const message = createBaseFiDepositAccountInitialSummary();
    message.summary = (object.summary !== undefined && object.summary !== null)
      ? Summary.fromPartial(object.summary)
      : undefined;
    message.lastTransactionTimestamp = object.lastTransactionTimestamp ?? 0;
    return message;
  },
};

function createBaseFiDepositAccountTransaction(): FiDepositAccountTransaction {
  return {
    Id: undefined,
    fipId: "",
    fiType: "",
    accountId: undefined,
    maskedAccNumber: "",
    transaction: undefined,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const FiDepositAccountTransaction: MessageFns<FiDepositAccountTransaction> = {
  encode(message: FiDepositAccountTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.fipId !== "") {
      writer.uint32(18).string(message.fipId);
    }
    if (message.fiType !== "") {
      writer.uint32(26).string(message.fiType);
    }
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(42).fork()).join();
    }
    if (message.maskedAccNumber !== "") {
      writer.uint32(50).string(message.maskedAccNumber);
    }
    if (message.transaction !== undefined) {
      Transaction.encode(message.transaction, writer.uint32(58).fork()).join();
    }
    if (message.createdAt !== 0) {
      writer.uint32(64).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(72).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): FiDepositAccountTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFiDepositAccountTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fipId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.fiType = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.maskedAccNumber = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.transaction = Transaction.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FiDepositAccountTransaction {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      fipId: isSet(object.fipId) ? globalThis.String(object.fipId) : "",
      fiType: isSet(object.fiType) ? globalThis.String(object.fiType) : "",
      accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined,
      maskedAccNumber: isSet(object.maskedAccNumber) ? globalThis.String(object.maskedAccNumber) : "",
      transaction: isSet(object.transaction) ? Transaction.fromJSON(object.transaction) : undefined,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: FiDepositAccountTransaction): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.fipId !== "") {
      obj.fipId = message.fipId;
    }
    if (message.fiType !== "") {
      obj.fiType = message.fiType;
    }
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    if (message.maskedAccNumber !== "") {
      obj.maskedAccNumber = message.maskedAccNumber;
    }
    if (message.transaction !== undefined) {
      obj.transaction = Transaction.toJSON(message.transaction);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FiDepositAccountTransaction>, I>>(base?: I): FiDepositAccountTransaction {
    return FiDepositAccountTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FiDepositAccountTransaction>, I>>(object: I): FiDepositAccountTransaction {
    const message = createBaseFiDepositAccountTransaction();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.fipId = object.fipId ?? "";
    message.fiType = object.fiType ?? "";
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    message.maskedAccNumber = object.maskedAccNumber ?? "";
    message.transaction = (object.transaction !== undefined && object.transaction !== null)
      ? Transaction.fromPartial(object.transaction)
      : undefined;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseDepositAccountDailyClosingBalance(): DepositAccountDailyClosingBalance {
  return {
    Id: undefined,
    accountId: undefined,
    summaryDate: 0,
    closingBalance: "",
    lastProcessedTransactionTime: 0,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const DepositAccountDailyClosingBalance: MessageFns<DepositAccountDailyClosingBalance> = {
  encode(message: DepositAccountDailyClosingBalance, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(18).fork()).join();
    }
    if (message.summaryDate !== 0) {
      writer.uint32(24).int64(message.summaryDate);
    }
    if (message.closingBalance !== "") {
      writer.uint32(34).string(message.closingBalance);
    }
    if (message.lastProcessedTransactionTime !== 0) {
      writer.uint32(40).int64(message.lastProcessedTransactionTime);
    }
    if (message.createdAt !== 0) {
      writer.uint32(48).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(56).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositAccountDailyClosingBalance {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositAccountDailyClosingBalance();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.summaryDate = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.closingBalance = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.lastProcessedTransactionTime = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 7: {
          if (tag !== 56) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositAccountDailyClosingBalance {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined,
      summaryDate: isSet(object.summaryDate) ? globalThis.Number(object.summaryDate) : 0,
      closingBalance: isSet(object.closingBalance) ? globalThis.String(object.closingBalance) : "",
      lastProcessedTransactionTime: isSet(object.lastProcessedTransactionTime)
        ? globalThis.Number(object.lastProcessedTransactionTime)
        : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: DepositAccountDailyClosingBalance): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    if (message.summaryDate !== 0) {
      obj.summaryDate = Math.round(message.summaryDate);
    }
    if (message.closingBalance !== "") {
      obj.closingBalance = message.closingBalance;
    }
    if (message.lastProcessedTransactionTime !== 0) {
      obj.lastProcessedTransactionTime = Math.round(message.lastProcessedTransactionTime);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositAccountDailyClosingBalance>, I>>(
    base?: I,
  ): DepositAccountDailyClosingBalance {
    return DepositAccountDailyClosingBalance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositAccountDailyClosingBalance>, I>>(
    object: I,
  ): DepositAccountDailyClosingBalance {
    const message = createBaseDepositAccountDailyClosingBalance();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    message.summaryDate = object.summaryDate ?? 0;
    message.closingBalance = object.closingBalance ?? "";
    message.lastProcessedTransactionTime = object.lastProcessedTransactionTime ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseDepositTxnCategoryMetadata(): DepositTxnCategoryMetadata {
  return {
    Id: undefined,
    accountId: undefined,
    status: "",
    transactionNarration: "",
    autoTransactionCategoryId: "",
    entity: [],
    purpose: [],
    ifsc: "",
    notes: "",
    matchedPattern: "",
    annotationToolStatus: "",
    createdAt: 0,
    updatedAt: 0,
  };
}

export const DepositTxnCategoryMetadata: MessageFns<DepositTxnCategoryMetadata> = {
  encode(message: DepositTxnCategoryMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(18).fork()).join();
    }
    if (message.status !== "") {
      writer.uint32(26).string(message.status);
    }
    if (message.transactionNarration !== "") {
      writer.uint32(34).string(message.transactionNarration);
    }
    if (message.autoTransactionCategoryId !== "") {
      writer.uint32(42).string(message.autoTransactionCategoryId);
    }
    for (const v of message.entity) {
      writer.uint32(50).string(v!);
    }
    for (const v of message.purpose) {
      writer.uint32(58).string(v!);
    }
    if (message.ifsc !== "") {
      writer.uint32(66).string(message.ifsc);
    }
    if (message.notes !== "") {
      writer.uint32(74).string(message.notes);
    }
    if (message.matchedPattern !== "") {
      writer.uint32(82).string(message.matchedPattern);
    }
    if (message.annotationToolStatus !== "") {
      writer.uint32(90).string(message.annotationToolStatus);
    }
    if (message.createdAt !== 0) {
      writer.uint32(96).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(104).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTxnCategoryMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTxnCategoryMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.transactionNarration = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.autoTransactionCategoryId = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.entity.push(reader.string());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.purpose.push(reader.string());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.ifsc = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.notes = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.matchedPattern = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.annotationToolStatus = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTxnCategoryMetadata {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined,
      status: isSet(object.status) ? globalThis.String(object.status) : "",
      transactionNarration: isSet(object.transactionNarration) ? globalThis.String(object.transactionNarration) : "",
      autoTransactionCategoryId: isSet(object.autoTransactionCategoryId)
        ? globalThis.String(object.autoTransactionCategoryId)
        : "",
      entity: globalThis.Array.isArray(object?.entity) ? object.entity.map((e: any) => globalThis.String(e)) : [],
      purpose: globalThis.Array.isArray(object?.purpose) ? object.purpose.map((e: any) => globalThis.String(e)) : [],
      ifsc: isSet(object.ifsc) ? globalThis.String(object.ifsc) : "",
      notes: isSet(object.notes) ? globalThis.String(object.notes) : "",
      matchedPattern: isSet(object.matchedPattern) ? globalThis.String(object.matchedPattern) : "",
      annotationToolStatus: isSet(object.annotationToolStatus) ? globalThis.String(object.annotationToolStatus) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: DepositTxnCategoryMetadata): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    if (message.transactionNarration !== "") {
      obj.transactionNarration = message.transactionNarration;
    }
    if (message.autoTransactionCategoryId !== "") {
      obj.autoTransactionCategoryId = message.autoTransactionCategoryId;
    }
    if (message.entity?.length) {
      obj.entity = message.entity;
    }
    if (message.purpose?.length) {
      obj.purpose = message.purpose;
    }
    if (message.ifsc !== "") {
      obj.ifsc = message.ifsc;
    }
    if (message.notes !== "") {
      obj.notes = message.notes;
    }
    if (message.matchedPattern !== "") {
      obj.matchedPattern = message.matchedPattern;
    }
    if (message.annotationToolStatus !== "") {
      obj.annotationToolStatus = message.annotationToolStatus;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTxnCategoryMetadata>, I>>(base?: I): DepositTxnCategoryMetadata {
    return DepositTxnCategoryMetadata.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTxnCategoryMetadata>, I>>(object: I): DepositTxnCategoryMetadata {
    const message = createBaseDepositTxnCategoryMetadata();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    message.status = object.status ?? "";
    message.transactionNarration = object.transactionNarration ?? "";
    message.autoTransactionCategoryId = object.autoTransactionCategoryId ?? "";
    message.entity = object.entity?.map((e) => e) || [];
    message.purpose = object.purpose?.map((e) => e) || [];
    message.ifsc = object.ifsc ?? "";
    message.notes = object.notes ?? "";
    message.matchedPattern = object.matchedPattern ?? "";
    message.annotationToolStatus = object.annotationToolStatus ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseCashFlowPeriod(): CashFlowPeriod {
  return { month: 0, year: 0 };
}

export const CashFlowPeriod: MessageFns<CashFlowPeriod> = {
  encode(message: CashFlowPeriod, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.month !== 0) {
      writer.uint32(8).int32(message.month);
    }
    if (message.year !== 0) {
      writer.uint32(16).int32(message.year);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CashFlowPeriod {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCashFlowPeriod();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.month = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.year = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CashFlowPeriod {
    return {
      month: isSet(object.month) ? globalThis.Number(object.month) : 0,
      year: isSet(object.year) ? globalThis.Number(object.year) : 0,
    };
  },

  toJSON(message: CashFlowPeriod): unknown {
    const obj: any = {};
    if (message.month !== 0) {
      obj.month = Math.round(message.month);
    }
    if (message.year !== 0) {
      obj.year = Math.round(message.year);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CashFlowPeriod>, I>>(base?: I): CashFlowPeriod {
    return CashFlowPeriod.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CashFlowPeriod>, I>>(object: I): CashFlowPeriod {
    const message = createBaseCashFlowPeriod();
    message.month = object.month ?? 0;
    message.year = object.year ?? 0;
    return message;
  },
};

function createBaseTxnFilters(): TxnFilters {
  return {
    timeRange: undefined,
    category: [],
    amountRange: undefined,
    merchant: [],
    narration: undefined,
    favorited: undefined,
    untagged: undefined,
    hasUserNotes: undefined,
    excludeCashFlow: undefined,
    txnType: undefined,
    hasRecurrentGroup: undefined,
    notes: undefined,
    recurrentGroups: [],
  };
}

export const TxnFilters: MessageFns<TxnFilters> = {
  encode(message: TxnFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.timeRange !== undefined) {
      TxnFilterTimeRange.encode(message.timeRange, writer.uint32(10).fork()).join();
    }
    for (const v of message.category) {
      TxnFilterCategory.encode(v!, writer.uint32(18).fork()).join();
    }
    if (message.amountRange !== undefined) {
      TxnFilterAmountRange.encode(message.amountRange, writer.uint32(26).fork()).join();
    }
    for (const v of message.merchant) {
      TxnFilterMerchant.encode(v!, writer.uint32(34).fork()).join();
    }
    if (message.narration !== undefined) {
      TxnFilterNarration.encode(message.narration, writer.uint32(42).fork()).join();
    }
    if (message.favorited !== undefined) {
      TxnFilterFavorited.encode(message.favorited, writer.uint32(50).fork()).join();
    }
    if (message.untagged !== undefined) {
      TxnFilterUntagged.encode(message.untagged, writer.uint32(58).fork()).join();
    }
    if (message.hasUserNotes !== undefined) {
      TxnFilterHasUserNotes.encode(message.hasUserNotes, writer.uint32(66).fork()).join();
    }
    if (message.excludeCashFlow !== undefined) {
      TxnFilterExcludeCashFlow.encode(message.excludeCashFlow, writer.uint32(74).fork()).join();
    }
    if (message.txnType !== undefined) {
      TxnFilterTxnType.encode(message.txnType, writer.uint32(82).fork()).join();
    }
    if (message.hasRecurrentGroup !== undefined) {
      TxnFilterHasRecurrentGroup.encode(message.hasRecurrentGroup, writer.uint32(90).fork()).join();
    }
    if (message.notes !== undefined) {
      TxnFilterNotes.encode(message.notes, writer.uint32(98).fork()).join();
    }
    for (const v of message.recurrentGroups) {
      TxnFilterRecurrentGroup.encode(v!, writer.uint32(106).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.timeRange = TxnFilterTimeRange.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.category.push(TxnFilterCategory.decode(reader, reader.uint32()));
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.amountRange = TxnFilterAmountRange.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.merchant.push(TxnFilterMerchant.decode(reader, reader.uint32()));
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.narration = TxnFilterNarration.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.favorited = TxnFilterFavorited.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.untagged = TxnFilterUntagged.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.hasUserNotes = TxnFilterHasUserNotes.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.excludeCashFlow = TxnFilterExcludeCashFlow.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.txnType = TxnFilterTxnType.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.hasRecurrentGroup = TxnFilterHasRecurrentGroup.decode(reader, reader.uint32());
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.notes = TxnFilterNotes.decode(reader, reader.uint32());
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.recurrentGroups.push(TxnFilterRecurrentGroup.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilters {
    return {
      timeRange: isSet(object.timeRange) ? TxnFilterTimeRange.fromJSON(object.timeRange) : undefined,
      category: globalThis.Array.isArray(object?.category)
        ? object.category.map((e: any) => TxnFilterCategory.fromJSON(e))
        : [],
      amountRange: isSet(object.amountRange) ? TxnFilterAmountRange.fromJSON(object.amountRange) : undefined,
      merchant: globalThis.Array.isArray(object?.merchant)
        ? object.merchant.map((e: any) => TxnFilterMerchant.fromJSON(e))
        : [],
      narration: isSet(object.narration) ? TxnFilterNarration.fromJSON(object.narration) : undefined,
      favorited: isSet(object.favorited) ? TxnFilterFavorited.fromJSON(object.favorited) : undefined,
      untagged: isSet(object.untagged) ? TxnFilterUntagged.fromJSON(object.untagged) : undefined,
      hasUserNotes: isSet(object.hasUserNotes) ? TxnFilterHasUserNotes.fromJSON(object.hasUserNotes) : undefined,
      excludeCashFlow: isSet(object.excludeCashFlow)
        ? TxnFilterExcludeCashFlow.fromJSON(object.excludeCashFlow)
        : undefined,
      txnType: isSet(object.txnType) ? TxnFilterTxnType.fromJSON(object.txnType) : undefined,
      hasRecurrentGroup: isSet(object.hasRecurrentGroup)
        ? TxnFilterHasRecurrentGroup.fromJSON(object.hasRecurrentGroup)
        : undefined,
      notes: isSet(object.notes) ? TxnFilterNotes.fromJSON(object.notes) : undefined,
      recurrentGroups: globalThis.Array.isArray(object?.recurrentGroups)
        ? object.recurrentGroups.map((e: any) => TxnFilterRecurrentGroup.fromJSON(e))
        : [],
    };
  },

  toJSON(message: TxnFilters): unknown {
    const obj: any = {};
    if (message.timeRange !== undefined) {
      obj.timeRange = TxnFilterTimeRange.toJSON(message.timeRange);
    }
    if (message.category?.length) {
      obj.category = message.category.map((e) => TxnFilterCategory.toJSON(e));
    }
    if (message.amountRange !== undefined) {
      obj.amountRange = TxnFilterAmountRange.toJSON(message.amountRange);
    }
    if (message.merchant?.length) {
      obj.merchant = message.merchant.map((e) => TxnFilterMerchant.toJSON(e));
    }
    if (message.narration !== undefined) {
      obj.narration = TxnFilterNarration.toJSON(message.narration);
    }
    if (message.favorited !== undefined) {
      obj.favorited = TxnFilterFavorited.toJSON(message.favorited);
    }
    if (message.untagged !== undefined) {
      obj.untagged = TxnFilterUntagged.toJSON(message.untagged);
    }
    if (message.hasUserNotes !== undefined) {
      obj.hasUserNotes = TxnFilterHasUserNotes.toJSON(message.hasUserNotes);
    }
    if (message.excludeCashFlow !== undefined) {
      obj.excludeCashFlow = TxnFilterExcludeCashFlow.toJSON(message.excludeCashFlow);
    }
    if (message.txnType !== undefined) {
      obj.txnType = TxnFilterTxnType.toJSON(message.txnType);
    }
    if (message.hasRecurrentGroup !== undefined) {
      obj.hasRecurrentGroup = TxnFilterHasRecurrentGroup.toJSON(message.hasRecurrentGroup);
    }
    if (message.notes !== undefined) {
      obj.notes = TxnFilterNotes.toJSON(message.notes);
    }
    if (message.recurrentGroups?.length) {
      obj.recurrentGroups = message.recurrentGroups.map((e) => TxnFilterRecurrentGroup.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilters>, I>>(base?: I): TxnFilters {
    return TxnFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilters>, I>>(object: I): TxnFilters {
    const message = createBaseTxnFilters();
    message.timeRange = (object.timeRange !== undefined && object.timeRange !== null)
      ? TxnFilterTimeRange.fromPartial(object.timeRange)
      : undefined;
    message.category = object.category?.map((e) => TxnFilterCategory.fromPartial(e)) || [];
    message.amountRange = (object.amountRange !== undefined && object.amountRange !== null)
      ? TxnFilterAmountRange.fromPartial(object.amountRange)
      : undefined;
    message.merchant = object.merchant?.map((e) => TxnFilterMerchant.fromPartial(e)) || [];
    message.narration = (object.narration !== undefined && object.narration !== null)
      ? TxnFilterNarration.fromPartial(object.narration)
      : undefined;
    message.favorited = (object.favorited !== undefined && object.favorited !== null)
      ? TxnFilterFavorited.fromPartial(object.favorited)
      : undefined;
    message.untagged = (object.untagged !== undefined && object.untagged !== null)
      ? TxnFilterUntagged.fromPartial(object.untagged)
      : undefined;
    message.hasUserNotes = (object.hasUserNotes !== undefined && object.hasUserNotes !== null)
      ? TxnFilterHasUserNotes.fromPartial(object.hasUserNotes)
      : undefined;
    message.excludeCashFlow = (object.excludeCashFlow !== undefined && object.excludeCashFlow !== null)
      ? TxnFilterExcludeCashFlow.fromPartial(object.excludeCashFlow)
      : undefined;
    message.txnType = (object.txnType !== undefined && object.txnType !== null)
      ? TxnFilterTxnType.fromPartial(object.txnType)
      : undefined;
    message.hasRecurrentGroup = (object.hasRecurrentGroup !== undefined && object.hasRecurrentGroup !== null)
      ? TxnFilterHasRecurrentGroup.fromPartial(object.hasRecurrentGroup)
      : undefined;
    message.notes = (object.notes !== undefined && object.notes !== null)
      ? TxnFilterNotes.fromPartial(object.notes)
      : undefined;
    message.recurrentGroups = object.recurrentGroups?.map((e) => TxnFilterRecurrentGroup.fromPartial(e)) || [];
    return message;
  },
};

function createBaseTxnFilterTimeRange(): TxnFilterTimeRange {
  return { fromTime: 0, toTime: 0 };
}

export const TxnFilterTimeRange: MessageFns<TxnFilterTimeRange> = {
  encode(message: TxnFilterTimeRange, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.fromTime !== 0) {
      writer.uint32(8).int64(message.fromTime);
    }
    if (message.toTime !== 0) {
      writer.uint32(16).int64(message.toTime);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterTimeRange {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterTimeRange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.fromTime = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.toTime = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterTimeRange {
    return {
      fromTime: isSet(object.fromTime) ? globalThis.Number(object.fromTime) : 0,
      toTime: isSet(object.toTime) ? globalThis.Number(object.toTime) : 0,
    };
  },

  toJSON(message: TxnFilterTimeRange): unknown {
    const obj: any = {};
    if (message.fromTime !== 0) {
      obj.fromTime = Math.round(message.fromTime);
    }
    if (message.toTime !== 0) {
      obj.toTime = Math.round(message.toTime);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterTimeRange>, I>>(base?: I): TxnFilterTimeRange {
    return TxnFilterTimeRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterTimeRange>, I>>(object: I): TxnFilterTimeRange {
    const message = createBaseTxnFilterTimeRange();
    message.fromTime = object.fromTime ?? 0;
    message.toTime = object.toTime ?? 0;
    return message;
  },
};

function createBaseTxnFilterCategory(): TxnFilterCategory {
  return { categoryCollection: "", categoryId: "", subcategoryId: "" };
}

export const TxnFilterCategory: MessageFns<TxnFilterCategory> = {
  encode(message: TxnFilterCategory, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.categoryCollection !== "") {
      writer.uint32(10).string(message.categoryCollection);
    }
    if (message.categoryId !== "") {
      writer.uint32(18).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(26).string(message.subcategoryId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterCategory {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterCategory();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterCategory {
    return {
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
    };
  },

  toJSON(message: TxnFilterCategory): unknown {
    const obj: any = {};
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterCategory>, I>>(base?: I): TxnFilterCategory {
    return TxnFilterCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterCategory>, I>>(object: I): TxnFilterCategory {
    const message = createBaseTxnFilterCategory();
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    return message;
  },
};

function createBaseTxnFilterAmountRange(): TxnFilterAmountRange {
  return { minAmount: 0, maxAmount: 0, includeForSearch: false };
}

export const TxnFilterAmountRange: MessageFns<TxnFilterAmountRange> = {
  encode(message: TxnFilterAmountRange, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.minAmount !== 0) {
      writer.uint32(8).int64(message.minAmount);
    }
    if (message.maxAmount !== 0) {
      writer.uint32(16).int64(message.maxAmount);
    }
    if (message.includeForSearch !== false) {
      writer.uint32(24).bool(message.includeForSearch);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterAmountRange {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterAmountRange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.minAmount = longToNumber(reader.int64());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.maxAmount = longToNumber(reader.int64());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.includeForSearch = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterAmountRange {
    return {
      minAmount: isSet(object.minAmount) ? globalThis.Number(object.minAmount) : 0,
      maxAmount: isSet(object.maxAmount) ? globalThis.Number(object.maxAmount) : 0,
      includeForSearch: isSet(object.includeForSearch) ? globalThis.Boolean(object.includeForSearch) : false,
    };
  },

  toJSON(message: TxnFilterAmountRange): unknown {
    const obj: any = {};
    if (message.minAmount !== 0) {
      obj.minAmount = Math.round(message.minAmount);
    }
    if (message.maxAmount !== 0) {
      obj.maxAmount = Math.round(message.maxAmount);
    }
    if (message.includeForSearch !== false) {
      obj.includeForSearch = message.includeForSearch;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterAmountRange>, I>>(base?: I): TxnFilterAmountRange {
    return TxnFilterAmountRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterAmountRange>, I>>(object: I): TxnFilterAmountRange {
    const message = createBaseTxnFilterAmountRange();
    message.minAmount = object.minAmount ?? 0;
    message.maxAmount = object.maxAmount ?? 0;
    message.includeForSearch = object.includeForSearch ?? false;
    return message;
  },
};

function createBaseTxnFilterMerchant(): TxnFilterMerchant {
  return { merchantId: "" };
}

export const TxnFilterMerchant: MessageFns<TxnFilterMerchant> = {
  encode(message: TxnFilterMerchant, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.merchantId !== "") {
      writer.uint32(10).string(message.merchantId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterMerchant {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterMerchant();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.merchantId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterMerchant {
    return { merchantId: isSet(object.merchantId) ? globalThis.String(object.merchantId) : "" };
  },

  toJSON(message: TxnFilterMerchant): unknown {
    const obj: any = {};
    if (message.merchantId !== "") {
      obj.merchantId = message.merchantId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterMerchant>, I>>(base?: I): TxnFilterMerchant {
    return TxnFilterMerchant.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterMerchant>, I>>(object: I): TxnFilterMerchant {
    const message = createBaseTxnFilterMerchant();
    message.merchantId = object.merchantId ?? "";
    return message;
  },
};

function createBaseTxnFilterNarration(): TxnFilterNarration {
  return { narration: "" };
}

export const TxnFilterNarration: MessageFns<TxnFilterNarration> = {
  encode(message: TxnFilterNarration, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.narration !== "") {
      writer.uint32(10).string(message.narration);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterNarration {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterNarration();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.narration = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterNarration {
    return { narration: isSet(object.narration) ? globalThis.String(object.narration) : "" };
  },

  toJSON(message: TxnFilterNarration): unknown {
    const obj: any = {};
    if (message.narration !== "") {
      obj.narration = message.narration;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterNarration>, I>>(base?: I): TxnFilterNarration {
    return TxnFilterNarration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterNarration>, I>>(object: I): TxnFilterNarration {
    const message = createBaseTxnFilterNarration();
    message.narration = object.narration ?? "";
    return message;
  },
};

function createBaseTxnFilterTxnType(): TxnFilterTxnType {
  return { txnType: "" };
}

export const TxnFilterTxnType: MessageFns<TxnFilterTxnType> = {
  encode(message: TxnFilterTxnType, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.txnType !== "") {
      writer.uint32(10).string(message.txnType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterTxnType {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterTxnType();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.txnType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterTxnType {
    return { txnType: isSet(object.txnType) ? globalThis.String(object.txnType) : "" };
  },

  toJSON(message: TxnFilterTxnType): unknown {
    const obj: any = {};
    if (message.txnType !== "") {
      obj.txnType = message.txnType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterTxnType>, I>>(base?: I): TxnFilterTxnType {
    return TxnFilterTxnType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterTxnType>, I>>(object: I): TxnFilterTxnType {
    const message = createBaseTxnFilterTxnType();
    message.txnType = object.txnType ?? "";
    return message;
  },
};

function createBaseTxnFilterFavorited(): TxnFilterFavorited {
  return { favorited: false };
}

export const TxnFilterFavorited: MessageFns<TxnFilterFavorited> = {
  encode(message: TxnFilterFavorited, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.favorited !== false) {
      writer.uint32(8).bool(message.favorited);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterFavorited {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterFavorited();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.favorited = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterFavorited {
    return { favorited: isSet(object.favorited) ? globalThis.Boolean(object.favorited) : false };
  },

  toJSON(message: TxnFilterFavorited): unknown {
    const obj: any = {};
    if (message.favorited !== false) {
      obj.favorited = message.favorited;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterFavorited>, I>>(base?: I): TxnFilterFavorited {
    return TxnFilterFavorited.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterFavorited>, I>>(object: I): TxnFilterFavorited {
    const message = createBaseTxnFilterFavorited();
    message.favorited = object.favorited ?? false;
    return message;
  },
};

function createBaseTxnFilterUntagged(): TxnFilterUntagged {
  return { untagged: false };
}

export const TxnFilterUntagged: MessageFns<TxnFilterUntagged> = {
  encode(message: TxnFilterUntagged, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.untagged !== false) {
      writer.uint32(8).bool(message.untagged);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterUntagged {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterUntagged();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.untagged = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterUntagged {
    return { untagged: isSet(object.untagged) ? globalThis.Boolean(object.untagged) : false };
  },

  toJSON(message: TxnFilterUntagged): unknown {
    const obj: any = {};
    if (message.untagged !== false) {
      obj.untagged = message.untagged;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterUntagged>, I>>(base?: I): TxnFilterUntagged {
    return TxnFilterUntagged.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterUntagged>, I>>(object: I): TxnFilterUntagged {
    const message = createBaseTxnFilterUntagged();
    message.untagged = object.untagged ?? false;
    return message;
  },
};

function createBaseTxnFilterHasUserNotes(): TxnFilterHasUserNotes {
  return { hasUserNotes: false };
}

export const TxnFilterHasUserNotes: MessageFns<TxnFilterHasUserNotes> = {
  encode(message: TxnFilterHasUserNotes, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasUserNotes !== false) {
      writer.uint32(8).bool(message.hasUserNotes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterHasUserNotes {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterHasUserNotes();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasUserNotes = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterHasUserNotes {
    return { hasUserNotes: isSet(object.hasUserNotes) ? globalThis.Boolean(object.hasUserNotes) : false };
  },

  toJSON(message: TxnFilterHasUserNotes): unknown {
    const obj: any = {};
    if (message.hasUserNotes !== false) {
      obj.hasUserNotes = message.hasUserNotes;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterHasUserNotes>, I>>(base?: I): TxnFilterHasUserNotes {
    return TxnFilterHasUserNotes.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterHasUserNotes>, I>>(object: I): TxnFilterHasUserNotes {
    const message = createBaseTxnFilterHasUserNotes();
    message.hasUserNotes = object.hasUserNotes ?? false;
    return message;
  },
};

function createBaseTxnFilterExcludeCashFlow(): TxnFilterExcludeCashFlow {
  return { excludeCashFlow: false };
}

export const TxnFilterExcludeCashFlow: MessageFns<TxnFilterExcludeCashFlow> = {
  encode(message: TxnFilterExcludeCashFlow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.excludeCashFlow !== false) {
      writer.uint32(8).bool(message.excludeCashFlow);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterExcludeCashFlow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterExcludeCashFlow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterExcludeCashFlow {
    return { excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false };
  },

  toJSON(message: TxnFilterExcludeCashFlow): unknown {
    const obj: any = {};
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterExcludeCashFlow>, I>>(base?: I): TxnFilterExcludeCashFlow {
    return TxnFilterExcludeCashFlow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterExcludeCashFlow>, I>>(object: I): TxnFilterExcludeCashFlow {
    const message = createBaseTxnFilterExcludeCashFlow();
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    return message;
  },
};

function createBaseTxnPaginationParams(): TxnPaginationParams {
  return { pageSize: 0, pageNumber: 0 };
}

export const TxnPaginationParams: MessageFns<TxnPaginationParams> = {
  encode(message: TxnPaginationParams, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.pageSize !== 0) {
      writer.uint32(8).uint32(message.pageSize);
    }
    if (message.pageNumber !== 0) {
      writer.uint32(16).uint32(message.pageNumber);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnPaginationParams {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnPaginationParams();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.pageSize = reader.uint32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.pageNumber = reader.uint32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnPaginationParams {
    return {
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
      pageNumber: isSet(object.pageNumber) ? globalThis.Number(object.pageNumber) : 0,
    };
  },

  toJSON(message: TxnPaginationParams): unknown {
    const obj: any = {};
    if (message.pageSize !== 0) {
      obj.pageSize = Math.round(message.pageSize);
    }
    if (message.pageNumber !== 0) {
      obj.pageNumber = Math.round(message.pageNumber);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnPaginationParams>, I>>(base?: I): TxnPaginationParams {
    return TxnPaginationParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnPaginationParams>, I>>(object: I): TxnPaginationParams {
    const message = createBaseTxnPaginationParams();
    message.pageSize = object.pageSize ?? 0;
    message.pageNumber = object.pageNumber ?? 0;
    return message;
  },
};

function createBaseTxnFilterHasRecurrentGroup(): TxnFilterHasRecurrentGroup {
  return { hasRecurrentGroup: false };
}

export const TxnFilterHasRecurrentGroup: MessageFns<TxnFilterHasRecurrentGroup> = {
  encode(message: TxnFilterHasRecurrentGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.hasRecurrentGroup !== false) {
      writer.uint32(8).bool(message.hasRecurrentGroup);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterHasRecurrentGroup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterHasRecurrentGroup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.hasRecurrentGroup = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterHasRecurrentGroup {
    return {
      hasRecurrentGroup: isSet(object.hasRecurrentGroup) ? globalThis.Boolean(object.hasRecurrentGroup) : false,
    };
  },

  toJSON(message: TxnFilterHasRecurrentGroup): unknown {
    const obj: any = {};
    if (message.hasRecurrentGroup !== false) {
      obj.hasRecurrentGroup = message.hasRecurrentGroup;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterHasRecurrentGroup>, I>>(base?: I): TxnFilterHasRecurrentGroup {
    return TxnFilterHasRecurrentGroup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterHasRecurrentGroup>, I>>(object: I): TxnFilterHasRecurrentGroup {
    const message = createBaseTxnFilterHasRecurrentGroup();
    message.hasRecurrentGroup = object.hasRecurrentGroup ?? false;
    return message;
  },
};

function createBaseTxnFilterRecurrentGroup(): TxnFilterRecurrentGroup {
  return { recurrentGroupId: "" };
}

export const TxnFilterRecurrentGroup: MessageFns<TxnFilterRecurrentGroup> = {
  encode(message: TxnFilterRecurrentGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.recurrentGroupId !== "") {
      writer.uint32(10).string(message.recurrentGroupId);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterRecurrentGroup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterRecurrentGroup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.recurrentGroupId = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterRecurrentGroup {
    return { recurrentGroupId: isSet(object.recurrentGroupId) ? globalThis.String(object.recurrentGroupId) : "" };
  },

  toJSON(message: TxnFilterRecurrentGroup): unknown {
    const obj: any = {};
    if (message.recurrentGroupId !== "") {
      obj.recurrentGroupId = message.recurrentGroupId;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterRecurrentGroup>, I>>(base?: I): TxnFilterRecurrentGroup {
    return TxnFilterRecurrentGroup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterRecurrentGroup>, I>>(object: I): TxnFilterRecurrentGroup {
    const message = createBaseTxnFilterRecurrentGroup();
    message.recurrentGroupId = object.recurrentGroupId ?? "";
    return message;
  },
};

function createBaseTxnFilterNotes(): TxnFilterNotes {
  return { notes: "" };
}

export const TxnFilterNotes: MessageFns<TxnFilterNotes> = {
  encode(message: TxnFilterNotes, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.notes !== "") {
      writer.uint32(10).string(message.notes);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): TxnFilterNotes {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTxnFilterNotes();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.notes = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): TxnFilterNotes {
    return { notes: isSet(object.notes) ? globalThis.String(object.notes) : "" };
  },

  toJSON(message: TxnFilterNotes): unknown {
    const obj: any = {};
    if (message.notes !== "") {
      obj.notes = message.notes;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<TxnFilterNotes>, I>>(base?: I): TxnFilterNotes {
    return TxnFilterNotes.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TxnFilterNotes>, I>>(object: I): TxnFilterNotes {
    const message = createBaseTxnFilterNotes();
    message.notes = object.notes ?? "";
    return message;
  },
};

function createBaseDepositTransaction(): DepositTransaction {
  return {
    Id: undefined,
    txnId: undefined,
    timestamp: 0,
    updateSource: 0,
    rawTransaction: undefined,
    status: "",
    entity: [],
    purpose: [],
    ifsc: "",
    userNotes: "",
    matchedPattern: "",
    categoryId: "",
    subcategoryId: "",
    categoryCollection: "",
    categoryUpdateSource: 0,
    categoryIdUpdatedAt: 0,
    merchantId: "",
    excludeCashFlow: false,
    favorite: false,
    createdAt: 0,
    predictionReason: "",
    cashflowAccountInPeriod: undefined,
    documents: [],
    recurrentGroupId: undefined,
    identifierKeywords: [],
  };
}

export const DepositTransaction: MessageFns<DepositTransaction> = {
  encode(message: DepositTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.txnId !== undefined) {
      ObjectId.encode(message.txnId, writer.uint32(18).fork()).join();
    }
    if (message.timestamp !== 0) {
      writer.uint32(24).int64(message.timestamp);
    }
    if (message.updateSource !== 0) {
      writer.uint32(32).int32(message.updateSource);
    }
    if (message.rawTransaction !== undefined) {
      FiDepositAccountTransaction.encode(message.rawTransaction, writer.uint32(42).fork()).join();
    }
    if (message.status !== "") {
      writer.uint32(50).string(message.status);
    }
    for (const v of message.entity) {
      writer.uint32(58).string(v!);
    }
    for (const v of message.purpose) {
      writer.uint32(66).string(v!);
    }
    if (message.ifsc !== "") {
      writer.uint32(74).string(message.ifsc);
    }
    if (message.userNotes !== "") {
      writer.uint32(82).string(message.userNotes);
    }
    if (message.matchedPattern !== "") {
      writer.uint32(90).string(message.matchedPattern);
    }
    if (message.categoryId !== "") {
      writer.uint32(98).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(106).string(message.subcategoryId);
    }
    if (message.categoryCollection !== "") {
      writer.uint32(114).string(message.categoryCollection);
    }
    if (message.categoryUpdateSource !== 0) {
      writer.uint32(1680).int32(message.categoryUpdateSource);
    }
    if (message.categoryIdUpdatedAt !== 0) {
      writer.uint32(880).int64(message.categoryIdUpdatedAt);
    }
    if (message.merchantId !== "") {
      writer.uint32(122).string(message.merchantId);
    }
    if (message.excludeCashFlow !== false) {
      writer.uint32(128).bool(message.excludeCashFlow);
    }
    if (message.favorite !== false) {
      writer.uint32(136).bool(message.favorite);
    }
    if (message.createdAt !== 0) {
      writer.uint32(144).int64(message.createdAt);
    }
    if (message.predictionReason !== "") {
      writer.uint32(154).string(message.predictionReason);
    }
    if (message.cashflowAccountInPeriod !== undefined) {
      CashFlowPeriod.encode(message.cashflowAccountInPeriod, writer.uint32(162).fork()).join();
    }
    for (const v of message.documents) {
      DepositTransaction_Document.encode(v!, writer.uint32(170).fork()).join();
    }
    if (message.recurrentGroupId !== undefined) {
      ObjectId.encode(message.recurrentGroupId, writer.uint32(178).fork()).join();
    }
    for (const v of message.identifierKeywords) {
      writer.uint32(186).string(v!);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.txnId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.timestamp = longToNumber(reader.int64());
          continue;
        }
        case 4: {
          if (tag !== 32) {
            break;
          }

          message.updateSource = reader.int32() as any;
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.rawTransaction = FiDepositAccountTransaction.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.entity.push(reader.string());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.purpose.push(reader.string());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.ifsc = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.userNotes = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.matchedPattern = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.categoryCollection = reader.string();
          continue;
        }
        case 210: {
          if (tag !== 1680) {
            break;
          }

          message.categoryUpdateSource = reader.int32() as any;
          continue;
        }
        case 110: {
          if (tag !== 880) {
            break;
          }

          message.categoryIdUpdatedAt = longToNumber(reader.int64());
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.merchantId = reader.string();
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
        case 18: {
          if (tag !== 144) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 19: {
          if (tag !== 154) {
            break;
          }

          message.predictionReason = reader.string();
          continue;
        }
        case 20: {
          if (tag !== 162) {
            break;
          }

          message.cashflowAccountInPeriod = CashFlowPeriod.decode(reader, reader.uint32());
          continue;
        }
        case 21: {
          if (tag !== 170) {
            break;
          }

          message.documents.push(DepositTransaction_Document.decode(reader, reader.uint32()));
          continue;
        }
        case 22: {
          if (tag !== 178) {
            break;
          }

          message.recurrentGroupId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 23: {
          if (tag !== 186) {
            break;
          }

          message.identifierKeywords.push(reader.string());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTransaction {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      txnId: isSet(object.txnId) ? ObjectId.fromJSON(object.txnId) : undefined,
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      updateSource: isSet(object.updateSource) ? depositTransaction_UpdateSourceFromJSON(object.updateSource) : 0,
      rawTransaction: isSet(object.rawTransaction)
        ? FiDepositAccountTransaction.fromJSON(object.rawTransaction)
        : undefined,
      status: isSet(object.status) ? globalThis.String(object.status) : "",
      entity: globalThis.Array.isArray(object?.entity) ? object.entity.map((e: any) => globalThis.String(e)) : [],
      purpose: globalThis.Array.isArray(object?.purpose) ? object.purpose.map((e: any) => globalThis.String(e)) : [],
      ifsc: isSet(object.ifsc) ? globalThis.String(object.ifsc) : "",
      userNotes: isSet(object.userNotes) ? globalThis.String(object.userNotes) : "",
      matchedPattern: isSet(object.matchedPattern) ? globalThis.String(object.matchedPattern) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
      categoryCollection: isSet(object.categoryCollection) ? globalThis.String(object.categoryCollection) : "",
      categoryUpdateSource: isSet(object.categoryUpdateSource)
        ? depositTxnCategoryUpdateSourceFromJSON(object.categoryUpdateSource)
        : 0,
      categoryIdUpdatedAt: isSet(object.categoryIdUpdatedAt) ? globalThis.Number(object.categoryIdUpdatedAt) : 0,
      merchantId: isSet(object.merchantId) ? globalThis.String(object.merchantId) : "",
      excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false,
      favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      predictionReason: isSet(object.predictionReason) ? globalThis.String(object.predictionReason) : "",
      cashflowAccountInPeriod: isSet(object.cashflowAccountInPeriod)
        ? CashFlowPeriod.fromJSON(object.cashflowAccountInPeriod)
        : undefined,
      documents: globalThis.Array.isArray(object?.documents)
        ? object.documents.map((e: any) => DepositTransaction_Document.fromJSON(e))
        : [],
      recurrentGroupId: isSet(object.recurrentGroupId) ? ObjectId.fromJSON(object.recurrentGroupId) : undefined,
      identifierKeywords: globalThis.Array.isArray(object?.identifierKeywords)
        ? object.identifierKeywords.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: DepositTransaction): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.txnId !== undefined) {
      obj.txnId = ObjectId.toJSON(message.txnId);
    }
    if (message.timestamp !== 0) {
      obj.timestamp = Math.round(message.timestamp);
    }
    if (message.updateSource !== 0) {
      obj.updateSource = depositTransaction_UpdateSourceToJSON(message.updateSource);
    }
    if (message.rawTransaction !== undefined) {
      obj.rawTransaction = FiDepositAccountTransaction.toJSON(message.rawTransaction);
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    if (message.entity?.length) {
      obj.entity = message.entity;
    }
    if (message.purpose?.length) {
      obj.purpose = message.purpose;
    }
    if (message.ifsc !== "") {
      obj.ifsc = message.ifsc;
    }
    if (message.userNotes !== "") {
      obj.userNotes = message.userNotes;
    }
    if (message.matchedPattern !== "") {
      obj.matchedPattern = message.matchedPattern;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    if (message.categoryCollection !== "") {
      obj.categoryCollection = message.categoryCollection;
    }
    if (message.categoryUpdateSource !== 0) {
      obj.categoryUpdateSource = depositTxnCategoryUpdateSourceToJSON(message.categoryUpdateSource);
    }
    if (message.categoryIdUpdatedAt !== 0) {
      obj.categoryIdUpdatedAt = Math.round(message.categoryIdUpdatedAt);
    }
    if (message.merchantId !== "") {
      obj.merchantId = message.merchantId;
    }
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.predictionReason !== "") {
      obj.predictionReason = message.predictionReason;
    }
    if (message.cashflowAccountInPeriod !== undefined) {
      obj.cashflowAccountInPeriod = CashFlowPeriod.toJSON(message.cashflowAccountInPeriod);
    }
    if (message.documents?.length) {
      obj.documents = message.documents.map((e) => DepositTransaction_Document.toJSON(e));
    }
    if (message.recurrentGroupId !== undefined) {
      obj.recurrentGroupId = ObjectId.toJSON(message.recurrentGroupId);
    }
    if (message.identifierKeywords?.length) {
      obj.identifierKeywords = message.identifierKeywords;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTransaction>, I>>(base?: I): DepositTransaction {
    return DepositTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTransaction>, I>>(object: I): DepositTransaction {
    const message = createBaseDepositTransaction();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.txnId = (object.txnId !== undefined && object.txnId !== null)
      ? ObjectId.fromPartial(object.txnId)
      : undefined;
    message.timestamp = object.timestamp ?? 0;
    message.updateSource = object.updateSource ?? 0;
    message.rawTransaction = (object.rawTransaction !== undefined && object.rawTransaction !== null)
      ? FiDepositAccountTransaction.fromPartial(object.rawTransaction)
      : undefined;
    message.status = object.status ?? "";
    message.entity = object.entity?.map((e) => e) || [];
    message.purpose = object.purpose?.map((e) => e) || [];
    message.ifsc = object.ifsc ?? "";
    message.userNotes = object.userNotes ?? "";
    message.matchedPattern = object.matchedPattern ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    message.categoryCollection = object.categoryCollection ?? "";
    message.categoryUpdateSource = object.categoryUpdateSource ?? 0;
    message.categoryIdUpdatedAt = object.categoryIdUpdatedAt ?? 0;
    message.merchantId = object.merchantId ?? "";
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    message.favorite = object.favorite ?? false;
    message.createdAt = object.createdAt ?? 0;
    message.predictionReason = object.predictionReason ?? "";
    message.cashflowAccountInPeriod =
      (object.cashflowAccountInPeriod !== undefined && object.cashflowAccountInPeriod !== null)
        ? CashFlowPeriod.fromPartial(object.cashflowAccountInPeriod)
        : undefined;
    message.documents = object.documents?.map((e) => DepositTransaction_Document.fromPartial(e)) || [];
    message.recurrentGroupId = (object.recurrentGroupId !== undefined && object.recurrentGroupId !== null)
      ? ObjectId.fromPartial(object.recurrentGroupId)
      : undefined;
    message.identifierKeywords = object.identifierKeywords?.map((e) => e) || [];
    return message;
  },
};

function createBaseDepositTransaction_Document(): DepositTransaction_Document {
  return { objectName: "", fileName: "", bucket: "", thumbnail: "", contentType: "" };
}

export const DepositTransaction_Document: MessageFns<DepositTransaction_Document> = {
  encode(message: DepositTransaction_Document, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.objectName !== "") {
      writer.uint32(10).string(message.objectName);
    }
    if (message.fileName !== "") {
      writer.uint32(18).string(message.fileName);
    }
    if (message.bucket !== "") {
      writer.uint32(26).string(message.bucket);
    }
    if (message.thumbnail !== "") {
      writer.uint32(34).string(message.thumbnail);
    }
    if (message.contentType !== "") {
      writer.uint32(42).string(message.contentType);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTransaction_Document {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTransaction_Document();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.objectName = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.fileName = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.bucket = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.thumbnail = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.contentType = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTransaction_Document {
    return {
      objectName: isSet(object.objectName) ? globalThis.String(object.objectName) : "",
      fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : "",
      bucket: isSet(object.bucket) ? globalThis.String(object.bucket) : "",
      thumbnail: isSet(object.thumbnail) ? globalThis.String(object.thumbnail) : "",
      contentType: isSet(object.contentType) ? globalThis.String(object.contentType) : "",
    };
  },

  toJSON(message: DepositTransaction_Document): unknown {
    const obj: any = {};
    if (message.objectName !== "") {
      obj.objectName = message.objectName;
    }
    if (message.fileName !== "") {
      obj.fileName = message.fileName;
    }
    if (message.bucket !== "") {
      obj.bucket = message.bucket;
    }
    if (message.thumbnail !== "") {
      obj.thumbnail = message.thumbnail;
    }
    if (message.contentType !== "") {
      obj.contentType = message.contentType;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTransaction_Document>, I>>(base?: I): DepositTransaction_Document {
    return DepositTransaction_Document.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTransaction_Document>, I>>(object: I): DepositTransaction_Document {
    const message = createBaseDepositTransaction_Document();
    message.objectName = object.objectName ?? "";
    message.fileName = object.fileName ?? "";
    message.bucket = object.bucket ?? "";
    message.thumbnail = object.thumbnail ?? "";
    message.contentType = object.contentType ?? "";
    return message;
  },
};

function createBaseRecurrentTransactionGroup(): RecurrentTransactionGroup {
  return {
    Id: undefined,
    accountId: undefined,
    subcategoryId: undefined,
    categoryId: undefined,
    txnType: "",
    txnMode: "",
    startTime: undefined,
    frequency: undefined,
    merchantId: undefined,
    favorite: false,
    excludeCashFlow: false,
    completed: false,
    userNotes: "",
    endDate: 0,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const RecurrentTransactionGroup: MessageFns<RecurrentTransactionGroup> = {
  encode(message: RecurrentTransactionGroup, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.accountId !== undefined) {
      ObjectId.encode(message.accountId, writer.uint32(18).fork()).join();
    }
    if (message.subcategoryId !== undefined) {
      ObjectId.encode(message.subcategoryId, writer.uint32(26).fork()).join();
    }
    if (message.categoryId !== undefined) {
      ObjectId.encode(message.categoryId, writer.uint32(34).fork()).join();
    }
    if (message.txnType !== "") {
      writer.uint32(42).string(message.txnType);
    }
    if (message.txnMode !== "") {
      writer.uint32(50).string(message.txnMode);
    }
    if (message.startTime !== undefined) {
      RecurrentGroupStartTime.encode(message.startTime, writer.uint32(66).fork()).join();
    }
    if (message.frequency !== undefined) {
      RecurrentGroupFrequency.encode(message.frequency, writer.uint32(74).fork()).join();
    }
    if (message.merchantId !== undefined) {
      ObjectId.encode(message.merchantId, writer.uint32(82).fork()).join();
    }
    if (message.favorite !== false) {
      writer.uint32(88).bool(message.favorite);
    }
    if (message.excludeCashFlow !== false) {
      writer.uint32(96).bool(message.excludeCashFlow);
    }
    if (message.completed !== false) {
      writer.uint32(104).bool(message.completed);
    }
    if (message.userNotes !== "") {
      writer.uint32(122).string(message.userNotes);
    }
    if (message.endDate !== 0) {
      writer.uint32(112).int64(message.endDate);
    }
    if (message.createdAt !== 0) {
      writer.uint32(128).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(136).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentTransactionGroup {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentTransactionGroup();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.accountId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.subcategoryId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.categoryId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.txnType = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.txnMode = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.startTime = RecurrentGroupStartTime.decode(reader, reader.uint32());
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.frequency = RecurrentGroupFrequency.decode(reader, reader.uint32());
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.merchantId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 11: {
          if (tag !== 88) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
        case 12: {
          if (tag !== 96) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
        case 13: {
          if (tag !== 104) {
            break;
          }

          message.completed = reader.bool();
          continue;
        }
        case 15: {
          if (tag !== 122) {
            break;
          }

          message.userNotes = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 112) {
            break;
          }

          message.endDate = longToNumber(reader.int64());
          continue;
        }
        case 16: {
          if (tag !== 128) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 17: {
          if (tag !== 136) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentTransactionGroup {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      accountId: isSet(object.accountId) ? ObjectId.fromJSON(object.accountId) : undefined,
      subcategoryId: isSet(object.subcategoryId) ? ObjectId.fromJSON(object.subcategoryId) : undefined,
      categoryId: isSet(object.categoryId) ? ObjectId.fromJSON(object.categoryId) : undefined,
      txnType: isSet(object.txnType) ? globalThis.String(object.txnType) : "",
      txnMode: isSet(object.txnMode) ? globalThis.String(object.txnMode) : "",
      startTime: isSet(object.startTime) ? RecurrentGroupStartTime.fromJSON(object.startTime) : undefined,
      frequency: isSet(object.frequency) ? RecurrentGroupFrequency.fromJSON(object.frequency) : undefined,
      merchantId: isSet(object.merchantId) ? ObjectId.fromJSON(object.merchantId) : undefined,
      favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false,
      excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false,
      completed: isSet(object.completed) ? globalThis.Boolean(object.completed) : false,
      userNotes: isSet(object.userNotes) ? globalThis.String(object.userNotes) : "",
      endDate: isSet(object.endDate) ? globalThis.Number(object.endDate) : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: RecurrentTransactionGroup): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.accountId !== undefined) {
      obj.accountId = ObjectId.toJSON(message.accountId);
    }
    if (message.subcategoryId !== undefined) {
      obj.subcategoryId = ObjectId.toJSON(message.subcategoryId);
    }
    if (message.categoryId !== undefined) {
      obj.categoryId = ObjectId.toJSON(message.categoryId);
    }
    if (message.txnType !== "") {
      obj.txnType = message.txnType;
    }
    if (message.txnMode !== "") {
      obj.txnMode = message.txnMode;
    }
    if (message.startTime !== undefined) {
      obj.startTime = RecurrentGroupStartTime.toJSON(message.startTime);
    }
    if (message.frequency !== undefined) {
      obj.frequency = RecurrentGroupFrequency.toJSON(message.frequency);
    }
    if (message.merchantId !== undefined) {
      obj.merchantId = ObjectId.toJSON(message.merchantId);
    }
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    if (message.completed !== false) {
      obj.completed = message.completed;
    }
    if (message.userNotes !== "") {
      obj.userNotes = message.userNotes;
    }
    if (message.endDate !== 0) {
      obj.endDate = Math.round(message.endDate);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentTransactionGroup>, I>>(base?: I): RecurrentTransactionGroup {
    return RecurrentTransactionGroup.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentTransactionGroup>, I>>(object: I): RecurrentTransactionGroup {
    const message = createBaseRecurrentTransactionGroup();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.accountId = (object.accountId !== undefined && object.accountId !== null)
      ? ObjectId.fromPartial(object.accountId)
      : undefined;
    message.subcategoryId = (object.subcategoryId !== undefined && object.subcategoryId !== null)
      ? ObjectId.fromPartial(object.subcategoryId)
      : undefined;
    message.categoryId = (object.categoryId !== undefined && object.categoryId !== null)
      ? ObjectId.fromPartial(object.categoryId)
      : undefined;
    message.txnType = object.txnType ?? "";
    message.txnMode = object.txnMode ?? "";
    message.startTime = (object.startTime !== undefined && object.startTime !== null)
      ? RecurrentGroupStartTime.fromPartial(object.startTime)
      : undefined;
    message.frequency = (object.frequency !== undefined && object.frequency !== null)
      ? RecurrentGroupFrequency.fromPartial(object.frequency)
      : undefined;
    message.merchantId = (object.merchantId !== undefined && object.merchantId !== null)
      ? ObjectId.fromPartial(object.merchantId)
      : undefined;
    message.favorite = object.favorite ?? false;
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    message.completed = object.completed ?? false;
    message.userNotes = object.userNotes ?? "";
    message.endDate = object.endDate ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseRecurrentGroupStartTime(): RecurrentGroupStartTime {
  return { date: undefined, dayOfWeek: 0 };
}

export const RecurrentGroupStartTime: MessageFns<RecurrentGroupStartTime> = {
  encode(message: RecurrentGroupStartTime, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.date !== undefined) {
      DateMessage.encode(message.date, writer.uint32(10).fork()).join();
    }
    if (message.dayOfWeek !== 0) {
      writer.uint32(16).int32(message.dayOfWeek);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupStartTime {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupStartTime();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.date = DateMessage.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.dayOfWeek = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupStartTime {
    return {
      date: isSet(object.date) ? DateMessage.fromJSON(object.date) : undefined,
      dayOfWeek: isSet(object.dayOfWeek) ? dayOfWeekFromJSON(object.dayOfWeek) : 0,
    };
  },

  toJSON(message: RecurrentGroupStartTime): unknown {
    const obj: any = {};
    if (message.date !== undefined) {
      obj.date = DateMessage.toJSON(message.date);
    }
    if (message.dayOfWeek !== 0) {
      obj.dayOfWeek = dayOfWeekToJSON(message.dayOfWeek);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupStartTime>, I>>(base?: I): RecurrentGroupStartTime {
    return RecurrentGroupStartTime.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupStartTime>, I>>(object: I): RecurrentGroupStartTime {
    const message = createBaseRecurrentGroupStartTime();
    message.date = (object.date !== undefined && object.date !== null)
      ? DateMessage.fromPartial(object.date)
      : undefined;
    message.dayOfWeek = object.dayOfWeek ?? 0;
    return message;
  },
};

function createBaseRecurrentGroupFrequency(): RecurrentGroupFrequency {
  return { repeats: 0, period: 0 };
}

export const RecurrentGroupFrequency: MessageFns<RecurrentGroupFrequency> = {
  encode(message: RecurrentGroupFrequency, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.repeats !== 0) {
      writer.uint32(8).int32(message.repeats);
    }
    if (message.period !== 0) {
      writer.uint32(16).int32(message.period);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupFrequency {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupFrequency();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.repeats = reader.int32();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.period = reader.int32() as any;
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupFrequency {
    return {
      repeats: isSet(object.repeats) ? globalThis.Number(object.repeats) : 0,
      period: isSet(object.period) ? calendarPeriodFromJSON(object.period) : 0,
    };
  },

  toJSON(message: RecurrentGroupFrequency): unknown {
    const obj: any = {};
    if (message.repeats !== 0) {
      obj.repeats = Math.round(message.repeats);
    }
    if (message.period !== 0) {
      obj.period = calendarPeriodToJSON(message.period);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupFrequency>, I>>(base?: I): RecurrentGroupFrequency {
    return RecurrentGroupFrequency.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupFrequency>, I>>(object: I): RecurrentGroupFrequency {
    const message = createBaseRecurrentGroupFrequency();
    message.repeats = object.repeats ?? 0;
    message.period = object.period ?? 0;
    return message;
  },
};

function createBaseRecurrentTxnGroupFilters(): RecurrentTxnGroupFilters {
  return { frequency: [], favorite: undefined, excludeCashFlow: undefined };
}

export const RecurrentTxnGroupFilters: MessageFns<RecurrentTxnGroupFilters> = {
  encode(message: RecurrentTxnGroupFilters, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    for (const v of message.frequency) {
      RecurrentGroupFrequency.encode(v!, writer.uint32(10).fork()).join();
    }
    if (message.favorite !== undefined) {
      RecurrentGroupFilterFavorite.encode(message.favorite, writer.uint32(18).fork()).join();
    }
    if (message.excludeCashFlow !== undefined) {
      RecurrentGroupFilterExcludeCashFlow.encode(message.excludeCashFlow, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentTxnGroupFilters {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentTxnGroupFilters();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.frequency.push(RecurrentGroupFrequency.decode(reader, reader.uint32()));
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.favorite = RecurrentGroupFilterFavorite.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.excludeCashFlow = RecurrentGroupFilterExcludeCashFlow.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentTxnGroupFilters {
    return {
      frequency: globalThis.Array.isArray(object?.frequency)
        ? object.frequency.map((e: any) => RecurrentGroupFrequency.fromJSON(e))
        : [],
      favorite: isSet(object.favorite) ? RecurrentGroupFilterFavorite.fromJSON(object.favorite) : undefined,
      excludeCashFlow: isSet(object.excludeCashFlow)
        ? RecurrentGroupFilterExcludeCashFlow.fromJSON(object.excludeCashFlow)
        : undefined,
    };
  },

  toJSON(message: RecurrentTxnGroupFilters): unknown {
    const obj: any = {};
    if (message.frequency?.length) {
      obj.frequency = message.frequency.map((e) => RecurrentGroupFrequency.toJSON(e));
    }
    if (message.favorite !== undefined) {
      obj.favorite = RecurrentGroupFilterFavorite.toJSON(message.favorite);
    }
    if (message.excludeCashFlow !== undefined) {
      obj.excludeCashFlow = RecurrentGroupFilterExcludeCashFlow.toJSON(message.excludeCashFlow);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentTxnGroupFilters>, I>>(base?: I): RecurrentTxnGroupFilters {
    return RecurrentTxnGroupFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentTxnGroupFilters>, I>>(object: I): RecurrentTxnGroupFilters {
    const message = createBaseRecurrentTxnGroupFilters();
    message.frequency = object.frequency?.map((e) => RecurrentGroupFrequency.fromPartial(e)) || [];
    message.favorite = (object.favorite !== undefined && object.favorite !== null)
      ? RecurrentGroupFilterFavorite.fromPartial(object.favorite)
      : undefined;
    message.excludeCashFlow = (object.excludeCashFlow !== undefined && object.excludeCashFlow !== null)
      ? RecurrentGroupFilterExcludeCashFlow.fromPartial(object.excludeCashFlow)
      : undefined;
    return message;
  },
};

function createBaseRecurrentGroupFilterFavorite(): RecurrentGroupFilterFavorite {
  return { favorite: false };
}

export const RecurrentGroupFilterFavorite: MessageFns<RecurrentGroupFilterFavorite> = {
  encode(message: RecurrentGroupFilterFavorite, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.favorite !== false) {
      writer.uint32(8).bool(message.favorite);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupFilterFavorite {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupFilterFavorite();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.favorite = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupFilterFavorite {
    return { favorite: isSet(object.favorite) ? globalThis.Boolean(object.favorite) : false };
  },

  toJSON(message: RecurrentGroupFilterFavorite): unknown {
    const obj: any = {};
    if (message.favorite !== false) {
      obj.favorite = message.favorite;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupFilterFavorite>, I>>(base?: I): RecurrentGroupFilterFavorite {
    return RecurrentGroupFilterFavorite.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupFilterFavorite>, I>>(object: I): RecurrentGroupFilterFavorite {
    const message = createBaseRecurrentGroupFilterFavorite();
    message.favorite = object.favorite ?? false;
    return message;
  },
};

function createBaseRecurrentGroupFilterExcludeCashFlow(): RecurrentGroupFilterExcludeCashFlow {
  return { excludeCashFlow: false };
}

export const RecurrentGroupFilterExcludeCashFlow: MessageFns<RecurrentGroupFilterExcludeCashFlow> = {
  encode(message: RecurrentGroupFilterExcludeCashFlow, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.excludeCashFlow !== false) {
      writer.uint32(8).bool(message.excludeCashFlow);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): RecurrentGroupFilterExcludeCashFlow {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRecurrentGroupFilterExcludeCashFlow();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.excludeCashFlow = reader.bool();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RecurrentGroupFilterExcludeCashFlow {
    return { excludeCashFlow: isSet(object.excludeCashFlow) ? globalThis.Boolean(object.excludeCashFlow) : false };
  },

  toJSON(message: RecurrentGroupFilterExcludeCashFlow): unknown {
    const obj: any = {};
    if (message.excludeCashFlow !== false) {
      obj.excludeCashFlow = message.excludeCashFlow;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RecurrentGroupFilterExcludeCashFlow>, I>>(
    base?: I,
  ): RecurrentGroupFilterExcludeCashFlow {
    return RecurrentGroupFilterExcludeCashFlow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RecurrentGroupFilterExcludeCashFlow>, I>>(
    object: I,
  ): RecurrentGroupFilterExcludeCashFlow {
    const message = createBaseRecurrentGroupFilterExcludeCashFlow();
    message.excludeCashFlow = object.excludeCashFlow ?? false;
    return message;
  },
};

function createBaseDeletedFiDepositAccountSummary(): DeletedFiDepositAccountSummary {
  return {};
}

export const DeletedFiDepositAccountSummary: MessageFns<DeletedFiDepositAccountSummary> = {
  encode(_: DeletedFiDepositAccountSummary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeletedFiDepositAccountSummary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeletedFiDepositAccountSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeletedFiDepositAccountSummary {
    return {};
  },

  toJSON(_: DeletedFiDepositAccountSummary): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeletedFiDepositAccountSummary>, I>>(base?: I): DeletedFiDepositAccountSummary {
    return DeletedFiDepositAccountSummary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletedFiDepositAccountSummary>, I>>(_: I): DeletedFiDepositAccountSummary {
    const message = createBaseDeletedFiDepositAccountSummary();
    return message;
  },
};

function createBaseDeletedFiDepositAccountTransaction(): DeletedFiDepositAccountTransaction {
  return {};
}

export const DeletedFiDepositAccountTransaction: MessageFns<DeletedFiDepositAccountTransaction> = {
  encode(_: DeletedFiDepositAccountTransaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeletedFiDepositAccountTransaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeletedFiDepositAccountTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeletedFiDepositAccountTransaction {
    return {};
  },

  toJSON(_: DeletedFiDepositAccountTransaction): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeletedFiDepositAccountTransaction>, I>>(
    base?: I,
  ): DeletedFiDepositAccountTransaction {
    return DeletedFiDepositAccountTransaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletedFiDepositAccountTransaction>, I>>(
    _: I,
  ): DeletedFiDepositAccountTransaction {
    const message = createBaseDeletedFiDepositAccountTransaction();
    return message;
  },
};

function createBaseDeletedFiDepositTxnCategoryMetadata(): DeletedFiDepositTxnCategoryMetadata {
  return {};
}

export const DeletedFiDepositTxnCategoryMetadata: MessageFns<DeletedFiDepositTxnCategoryMetadata> = {
  encode(_: DeletedFiDepositTxnCategoryMetadata, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DeletedFiDepositTxnCategoryMetadata {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDeletedFiDepositTxnCategoryMetadata();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(_: any): DeletedFiDepositTxnCategoryMetadata {
    return {};
  },

  toJSON(_: DeletedFiDepositTxnCategoryMetadata): unknown {
    const obj: any = {};
    return obj;
  },

  create<I extends Exact<DeepPartial<DeletedFiDepositTxnCategoryMetadata>, I>>(
    base?: I,
  ): DeletedFiDepositTxnCategoryMetadata {
    return DeletedFiDepositTxnCategoryMetadata.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletedFiDepositTxnCategoryMetadata>, I>>(
    _: I,
  ): DeletedFiDepositTxnCategoryMetadata {
    const message = createBaseDeletedFiDepositTxnCategoryMetadata();
    return message;
  },
};

function createBaseDepositTxnUserPersonalizedTagFreq(): DepositTxnUserPersonalizedTagFreq {
  return {
    Id: undefined,
    keyword: "",
    categoryId: "",
    subcategoryId: "",
    frequency: 0,
    type: "",
    userId: undefined,
    familyId: "",
    createdAt: 0,
    updatedAt: 0,
  };
}

export const DepositTxnUserPersonalizedTagFreq: MessageFns<DepositTxnUserPersonalizedTagFreq> = {
  encode(message: DepositTxnUserPersonalizedTagFreq, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.keyword !== "") {
      writer.uint32(18).string(message.keyword);
    }
    if (message.categoryId !== "") {
      writer.uint32(26).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(34).string(message.subcategoryId);
    }
    if (message.frequency !== 0) {
      writer.uint32(40).int64(message.frequency);
    }
    if (message.type !== "") {
      writer.uint32(50).string(message.type);
    }
    if (message.userId !== undefined) {
      ObjectId.encode(message.userId, writer.uint32(58).fork()).join();
    }
    if (message.familyId !== "") {
      writer.uint32(66).string(message.familyId);
    }
    if (message.createdAt !== 0) {
      writer.uint32(808).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(816).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTxnUserPersonalizedTagFreq {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTxnUserPersonalizedTagFreq();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.frequency = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.userId = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.familyId = reader.string();
          continue;
        }
        case 101: {
          if (tag !== 808) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 102: {
          if (tag !== 816) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTxnUserPersonalizedTagFreq {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
      frequency: isSet(object.frequency) ? globalThis.Number(object.frequency) : 0,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      userId: isSet(object.userId) ? ObjectId.fromJSON(object.userId) : undefined,
      familyId: isSet(object.familyId) ? globalThis.String(object.familyId) : "",
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: DepositTxnUserPersonalizedTagFreq): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    if (message.frequency !== 0) {
      obj.frequency = Math.round(message.frequency);
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.userId !== undefined) {
      obj.userId = ObjectId.toJSON(message.userId);
    }
    if (message.familyId !== "") {
      obj.familyId = message.familyId;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTxnUserPersonalizedTagFreq>, I>>(
    base?: I,
  ): DepositTxnUserPersonalizedTagFreq {
    return DepositTxnUserPersonalizedTagFreq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTxnUserPersonalizedTagFreq>, I>>(
    object: I,
  ): DepositTxnUserPersonalizedTagFreq {
    const message = createBaseDepositTxnUserPersonalizedTagFreq();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.keyword = object.keyword ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    message.frequency = object.frequency ?? 0;
    message.type = object.type ?? "";
    message.userId = (object.userId !== undefined && object.userId !== null)
      ? ObjectId.fromPartial(object.userId)
      : undefined;
    message.familyId = object.familyId ?? "";
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseDepositTxnLlmPredictedTagFreq(): DepositTxnLlmPredictedTagFreq {
  return {
    Id: undefined,
    keyword: "",
    categoryId: "",
    subcategoryId: "",
    type: "",
    frequency: 0,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const DepositTxnLlmPredictedTagFreq: MessageFns<DepositTxnLlmPredictedTagFreq> = {
  encode(message: DepositTxnLlmPredictedTagFreq, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.keyword !== "") {
      writer.uint32(18).string(message.keyword);
    }
    if (message.categoryId !== "") {
      writer.uint32(26).string(message.categoryId);
    }
    if (message.subcategoryId !== "") {
      writer.uint32(34).string(message.subcategoryId);
    }
    if (message.type !== "") {
      writer.uint32(42).string(message.type);
    }
    if (message.frequency !== 0) {
      writer.uint32(48).int64(message.frequency);
    }
    if (message.createdAt !== 0) {
      writer.uint32(808).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(816).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): DepositTxnLlmPredictedTagFreq {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDepositTxnLlmPredictedTagFreq();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.categoryId = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.subcategoryId = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.frequency = longToNumber(reader.int64());
          continue;
        }
        case 101: {
          if (tag !== 808) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 102: {
          if (tag !== 816) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): DepositTxnLlmPredictedTagFreq {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      categoryId: isSet(object.categoryId) ? globalThis.String(object.categoryId) : "",
      subcategoryId: isSet(object.subcategoryId) ? globalThis.String(object.subcategoryId) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      frequency: isSet(object.frequency) ? globalThis.Number(object.frequency) : 0,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: DepositTxnLlmPredictedTagFreq): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.categoryId !== "") {
      obj.categoryId = message.categoryId;
    }
    if (message.subcategoryId !== "") {
      obj.subcategoryId = message.subcategoryId;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.frequency !== 0) {
      obj.frequency = Math.round(message.frequency);
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<DepositTxnLlmPredictedTagFreq>, I>>(base?: I): DepositTxnLlmPredictedTagFreq {
    return DepositTxnLlmPredictedTagFreq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DepositTxnLlmPredictedTagFreq>, I>>(
    object: I,
  ): DepositTxnLlmPredictedTagFreq {
    const message = createBaseDepositTxnLlmPredictedTagFreq();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.keyword = object.keyword ?? "";
    message.categoryId = object.categoryId ?? "";
    message.subcategoryId = object.subcategoryId ?? "";
    message.type = object.type ?? "";
    message.frequency = object.frequency ?? 0;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

function createBaseUntaggedDepositTxnAnnotatorReview(): UntaggedDepositTxnAnnotatorReview {
  return {
    Id: undefined,
    keyword: "",
    type: "",
    txnNarrations: [],
    txnCount: 0,
    taggedTxnWhoseTagsWillChange: 0,
    isProcessedByAnnotator: false,
    isProcessedByReviewer: false,
    createdAt: 0,
    updatedAt: 0,
  };
}

export const UntaggedDepositTxnAnnotatorReview: MessageFns<UntaggedDepositTxnAnnotatorReview> = {
  encode(message: UntaggedDepositTxnAnnotatorReview, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.Id !== undefined) {
      ObjectId.encode(message.Id, writer.uint32(10).fork()).join();
    }
    if (message.keyword !== "") {
      writer.uint32(18).string(message.keyword);
    }
    if (message.type !== "") {
      writer.uint32(26).string(message.type);
    }
    for (const v of message.txnNarrations) {
      writer.uint32(34).string(v!);
    }
    if (message.txnCount !== 0) {
      writer.uint32(40).int64(message.txnCount);
    }
    if (message.taggedTxnWhoseTagsWillChange !== 0) {
      writer.uint32(48).int64(message.taggedTxnWhoseTagsWillChange);
    }
    if (message.isProcessedByAnnotator !== false) {
      writer.uint32(64).bool(message.isProcessedByAnnotator);
    }
    if (message.isProcessedByReviewer !== false) {
      writer.uint32(72).bool(message.isProcessedByReviewer);
    }
    if (message.createdAt !== 0) {
      writer.uint32(808).int64(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      writer.uint32(816).int64(message.updatedAt);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): UntaggedDepositTxnAnnotatorReview {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUntaggedDepositTxnAnnotatorReview();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.Id = ObjectId.decode(reader, reader.uint32());
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.keyword = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.txnNarrations.push(reader.string());
          continue;
        }
        case 5: {
          if (tag !== 40) {
            break;
          }

          message.txnCount = longToNumber(reader.int64());
          continue;
        }
        case 6: {
          if (tag !== 48) {
            break;
          }

          message.taggedTxnWhoseTagsWillChange = longToNumber(reader.int64());
          continue;
        }
        case 8: {
          if (tag !== 64) {
            break;
          }

          message.isProcessedByAnnotator = reader.bool();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.isProcessedByReviewer = reader.bool();
          continue;
        }
        case 101: {
          if (tag !== 808) {
            break;
          }

          message.createdAt = longToNumber(reader.int64());
          continue;
        }
        case 102: {
          if (tag !== 816) {
            break;
          }

          message.updatedAt = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UntaggedDepositTxnAnnotatorReview {
    return {
      Id: isSet(object.Id) ? ObjectId.fromJSON(object.Id) : undefined,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : "",
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      txnNarrations: globalThis.Array.isArray(object?.txnNarrations)
        ? object.txnNarrations.map((e: any) => globalThis.String(e))
        : [],
      txnCount: isSet(object.txnCount) ? globalThis.Number(object.txnCount) : 0,
      taggedTxnWhoseTagsWillChange: isSet(object.taggedTxnWhoseTagsWillChange)
        ? globalThis.Number(object.taggedTxnWhoseTagsWillChange)
        : 0,
      isProcessedByAnnotator: isSet(object.isProcessedByAnnotator)
        ? globalThis.Boolean(object.isProcessedByAnnotator)
        : false,
      isProcessedByReviewer: isSet(object.isProcessedByReviewer)
        ? globalThis.Boolean(object.isProcessedByReviewer)
        : false,
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0,
      updatedAt: isSet(object.updatedAt) ? globalThis.Number(object.updatedAt) : 0,
    };
  },

  toJSON(message: UntaggedDepositTxnAnnotatorReview): unknown {
    const obj: any = {};
    if (message.Id !== undefined) {
      obj.Id = ObjectId.toJSON(message.Id);
    }
    if (message.keyword !== "") {
      obj.keyword = message.keyword;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.txnNarrations?.length) {
      obj.txnNarrations = message.txnNarrations;
    }
    if (message.txnCount !== 0) {
      obj.txnCount = Math.round(message.txnCount);
    }
    if (message.taggedTxnWhoseTagsWillChange !== 0) {
      obj.taggedTxnWhoseTagsWillChange = Math.round(message.taggedTxnWhoseTagsWillChange);
    }
    if (message.isProcessedByAnnotator !== false) {
      obj.isProcessedByAnnotator = message.isProcessedByAnnotator;
    }
    if (message.isProcessedByReviewer !== false) {
      obj.isProcessedByReviewer = message.isProcessedByReviewer;
    }
    if (message.createdAt !== 0) {
      obj.createdAt = Math.round(message.createdAt);
    }
    if (message.updatedAt !== 0) {
      obj.updatedAt = Math.round(message.updatedAt);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UntaggedDepositTxnAnnotatorReview>, I>>(
    base?: I,
  ): UntaggedDepositTxnAnnotatorReview {
    return UntaggedDepositTxnAnnotatorReview.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UntaggedDepositTxnAnnotatorReview>, I>>(
    object: I,
  ): UntaggedDepositTxnAnnotatorReview {
    const message = createBaseUntaggedDepositTxnAnnotatorReview();
    message.Id = (object.Id !== undefined && object.Id !== null) ? ObjectId.fromPartial(object.Id) : undefined;
    message.keyword = object.keyword ?? "";
    message.type = object.type ?? "";
    message.txnNarrations = object.txnNarrations?.map((e) => e) || [];
    message.txnCount = object.txnCount ?? 0;
    message.taggedTxnWhoseTagsWillChange = object.taggedTxnWhoseTagsWillChange ?? 0;
    message.isProcessedByAnnotator = object.isProcessedByAnnotator ?? false;
    message.isProcessedByReviewer = object.isProcessedByReviewer ?? false;
    message.createdAt = object.createdAt ?? 0;
    message.updatedAt = object.updatedAt ?? 0;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
