// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.31.1
// source: rebit/fi_schemas/deposit/deposit.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire";

export const protobufPackage = "rebit.fi_schemas.deposit";

export enum AccountType {
  /** SAVINGS - Default */
  SAVINGS = 0,
  CURRENT = 1,
  UNRECOGNIZED = -1,
}

export function accountTypeFromJSON(object: any): AccountType {
  switch (object) {
    case 0:
    case "SAVINGS":
      return AccountType.SAVINGS;
    case 1:
    case "CURRENT":
      return AccountType.CURRENT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AccountType.UNRECOGNIZED;
  }
}

export function accountTypeToJSON(object: AccountType): string {
  switch (object) {
    case AccountType.SAVINGS:
      return "SAVINGS";
    case AccountType.CURRENT:
      return "CURRENT";
    case AccountType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum HoldersType {
  /** SINGLE - Default */
  SINGLE = 0,
  JOINT = 1,
  UNRECOGNIZED = -1,
}

export function holdersTypeFromJSON(object: any): HoldersType {
  switch (object) {
    case 0:
    case "SINGLE":
      return HoldersType.SINGLE;
    case 1:
    case "JOINT":
      return HoldersType.JOINT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HoldersType.UNRECOGNIZED;
  }
}

export function holdersTypeToJSON(object: HoldersType): string {
  switch (object) {
    case HoldersType.SINGLE:
      return "SINGLE";
    case HoldersType.JOINT:
      return "JOINT";
    case HoldersType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum HoldingNominee {
  /** REGISTERED - Default */
  REGISTERED = 0,
  NOT_REGISTERED = 1,
  UNRECOGNIZED = -1,
}

export function holdingNomineeFromJSON(object: any): HoldingNominee {
  switch (object) {
    case 0:
    case "REGISTERED":
      return HoldingNominee.REGISTERED;
    case 1:
    case "NOT_REGISTERED":
      return HoldingNominee.NOT_REGISTERED;
    case -1:
    case "UNRECOGNIZED":
    default:
      return HoldingNominee.UNRECOGNIZED;
  }
}

export function holdingNomineeToJSON(object: HoldingNominee): string {
  switch (object) {
    case HoldingNominee.REGISTERED:
      return "REGISTERED";
    case HoldingNominee.NOT_REGISTERED:
      return "NOT_REGISTERED";
    case HoldingNominee.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum StatusTypes {
  /** ACTIVE - Default */
  ACTIVE = 0,
  INACTIVE = 1,
  UNRECOGNIZED = -1,
}

export function statusTypesFromJSON(object: any): StatusTypes {
  switch (object) {
    case 0:
    case "ACTIVE":
      return StatusTypes.ACTIVE;
    case 1:
    case "INACTIVE":
      return StatusTypes.INACTIVE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return StatusTypes.UNRECOGNIZED;
  }
}

export function statusTypesToJSON(object: StatusTypes): string {
  switch (object) {
    case StatusTypes.ACTIVE:
      return "ACTIVE";
    case StatusTypes.INACTIVE:
      return "INACTIVE";
    case StatusTypes.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SummaryFacility {
  /** OD - Default */
  OD = 0,
  CC = 1,
  UNRECOGNIZED = -1,
}

export function summaryFacilityFromJSON(object: any): SummaryFacility {
  switch (object) {
    case 0:
    case "OD":
      return SummaryFacility.OD;
    case 1:
    case "CC":
      return SummaryFacility.CC;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SummaryFacility.UNRECOGNIZED;
  }
}

export function summaryFacilityToJSON(object: SummaryFacility): string {
  switch (object) {
    case SummaryFacility.OD:
      return "OD";
    case SummaryFacility.CC:
      return "CC";
    case SummaryFacility.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum TransactionMode {
  /** CASH - Default */
  CASH = 0,
  ATM = 1,
  CARD = 2,
  UPI = 3,
  FT = 4,
  OTHERS = 5,
  UNRECOGNIZED = -1,
}

export function transactionModeFromJSON(object: any): TransactionMode {
  switch (object) {
    case 0:
    case "CASH":
      return TransactionMode.CASH;
    case 1:
    case "ATM":
      return TransactionMode.ATM;
    case 2:
    case "CARD":
      return TransactionMode.CARD;
    case 3:
    case "UPI":
      return TransactionMode.UPI;
    case 4:
    case "FT":
      return TransactionMode.FT;
    case 5:
    case "OTHERS":
      return TransactionMode.OTHERS;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TransactionMode.UNRECOGNIZED;
  }
}

export function transactionModeToJSON(object: TransactionMode): string {
  switch (object) {
    case TransactionMode.CASH:
      return "CASH";
    case TransactionMode.ATM:
      return "ATM";
    case TransactionMode.CARD:
      return "CARD";
    case TransactionMode.UPI:
      return "UPI";
    case TransactionMode.FT:
      return "FT";
    case TransactionMode.OTHERS:
      return "OTHERS";
    case TransactionMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum TransactionType {
  /** CREDIT - Default */
  CREDIT = 0,
  DEBIT = 1,
  UNRECOGNIZED = -1,
}

export function transactionTypeFromJSON(object: any): TransactionType {
  switch (object) {
    case 0:
    case "CREDIT":
      return TransactionType.CREDIT;
    case 1:
    case "DEBIT":
      return TransactionType.DEBIT;
    case -1:
    case "UNRECOGNIZED":
    default:
      return TransactionType.UNRECOGNIZED;
  }
}

export function transactionTypeToJSON(object: TransactionType): string {
  switch (object) {
    case TransactionType.CREDIT:
      return "CREDIT";
    case TransactionType.DEBIT:
      return "DEBIT";
    case TransactionType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

/** Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type. */
export interface Profile {
  /** @gotags: json:"Holders" */
  holders: Holders | undefined;
}

export interface Account {
  /** @gotags: json:"type" */
  type: string;
  /** @gotags: json:"maskedAccNumber" */
  maskedAccNumber: string;
  /** @gotags: json:"version,string" */
  version: number;
  /** @gotags: json:"linkedAccRef" */
  linkedAccRef: string;
  /** Basic Profile of the account which should include the Account Owner information, maskedAccNumber and linkedAccRef numbers, type of account specific to the FI type and any other generic details as might be pertinent for the specified FI type. */
  profile:
    | Profile
    | undefined;
  /** The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account. */
  summary:
    | Summary
    | undefined;
  /** Details of all transactions that have been posted in an account. */
  transactions: Transactions | undefined;
}

/** Personal details of the deposit account holder */
export interface Holder {
  /** Name of account holder */
  name: string;
  /** Date of birth of account holder */
  dob:
    | Holder_DateOfBirth
    | undefined;
  /** Mobile number of account holder */
  mobile: string;
  /** Status of nominee registered with the account */
  nominee: string;
  /** Landline number of account holder */
  landline: string;
  /** Address of account holder */
  address: string;
  /** Email ID of account holder */
  email: string;
  /** PAN number of account holder */
  pan: string;
  /** KYC status whether its completed or pending */
  ckycCompliance: boolean;
  /**
   * Type of account held by an individual whether its single or jointly operated.
   * Note: This differs between TSP and ReBIT
   */
  type: string;
  /** Note: This differs between TSP and ReBIT */
  maskedAccNumber: string;
  /** @gotags: json:"linkedAccRef" */
  linkedAccRef: string;
}

export interface Holder_DateOfBirth {
  value: number;
}

export interface Holders {
  /** Type of account held by an individual whether its single or jointly operated. */
  type: string;
  /** Personal details of the deposit account holder */
  holder: Holder[];
}

export interface Pending {
  /** The amount invested or to be invested in deposits such as Fixed or Recurring. */
  transactionType: string;
  /** Pending amount */
  amount: number;
}

/** The value of the account, term of the deposits if relevant and any other data that summarizes the funds in the account. */
export interface Summary {
  /** Available Balance. */
  currentBalance: string;
  /** Currency in which transaction taken place. */
  currency: string;
  /** Currency conversion exchange rate for the day. */
  exchgeRate: string;
  /** Date and time stamp for which current balance recorded. */
  balanceDateTime:
    | Summary_BalanceDateTime
    | undefined;
  /** Type of account whether is saving or current */
  type: string;
  /** Location of branch where investment was made */
  branch: string;
  /** Additional facility like Overdraft or Sweep In applicable for the given account. */
  facility: string;
  /** IFSC code of the issued bank branch */
  ifscCode: string;
  /** MICR code is uniquely identifies a bank and a branch participating in an ECS. */
  micrCode: string;
  /** Opening date of the deposit account */
  openingDate: string;
  /** The portion of the sanctioned limit that is available to be drawn, as of this point in time */
  currentODLimit: string;
  /** Sanctioned limit (overall limit given by the bank, on the OD/CC facility). */
  drawingLimit: string;
  /** An account status with either active or inactive. An inactive account includes the dormant, inactive, or closed account. */
  status: string;
  /**
   * Note: This differs between TSP and ReBIT
   * The amount invested or to be invested in deposits such as Fixed or Recurring.
   */
  pendingTransactionType: string;
  /** Pending amount */
  pendingAmount: number;
}

export interface Summary_BalanceDateTime {
  value: number;
}

export interface Transaction {
  /** Type of account transaction either debit or credit */
  type: string;
  /** Mode of investment captured whether online/demat or physical form. */
  mode: string;
  /** Amount of transaction. */
  amount: number;
  /** Available balance. */
  currentBalance: number;
  /** Transaction date time stamp for particular record when transaction taken place */
  transactionTimestamp:
    | Transaction_TransactionTimestamp
    | undefined;
  /** @gotags: json:"valueDate" */
  valueDate:
    | Transaction_TransactionTimestamp
    | undefined;
  /** Unique id of the transaction */
  txnId: string;
  /** Narration is additional details in form of description of remark associated with investment. */
  narration: string;
  /** The cheque or reference no for the given transaction. */
  reference: string;
}

export interface Transaction_TransactionTimestamp {
  value: number;
}

/** Details of all transactions that have been posted in an account. */
export interface Transactions {
  /** Start date of transaction or period for which details are require. */
  startDate: string;
  /** End date of transaction or period for which details are require. */
  endDate: string;
  /** @gotags: json:"Transaction" */
  transaction: Transaction[];
}

function createBaseProfile(): Profile {
  return { holders: undefined };
}

export const Profile: MessageFns<Profile> = {
  encode(message: Profile, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.holders !== undefined) {
      Holders.encode(message.holders, writer.uint32(10).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Profile {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProfile();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.holders = Holders.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Profile {
    return { holders: isSet(object.holders) ? Holders.fromJSON(object.holders) : undefined };
  },

  toJSON(message: Profile): unknown {
    const obj: any = {};
    if (message.holders !== undefined) {
      obj.holders = Holders.toJSON(message.holders);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Profile>, I>>(base?: I): Profile {
    return Profile.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Profile>, I>>(object: I): Profile {
    const message = createBaseProfile();
    message.holders = (object.holders !== undefined && object.holders !== null)
      ? Holders.fromPartial(object.holders)
      : undefined;
    return message;
  },
};

function createBaseAccount(): Account {
  return {
    type: "",
    maskedAccNumber: "",
    version: 0,
    linkedAccRef: "",
    profile: undefined,
    summary: undefined,
    transactions: undefined,
  };
}

export const Account: MessageFns<Account> = {
  encode(message: Account, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== "") {
      writer.uint32(10).string(message.type);
    }
    if (message.maskedAccNumber !== "") {
      writer.uint32(18).string(message.maskedAccNumber);
    }
    if (message.version !== 0) {
      writer.uint32(29).float(message.version);
    }
    if (message.linkedAccRef !== "") {
      writer.uint32(34).string(message.linkedAccRef);
    }
    if (message.profile !== undefined) {
      Profile.encode(message.profile, writer.uint32(42).fork()).join();
    }
    if (message.summary !== undefined) {
      Summary.encode(message.summary, writer.uint32(50).fork()).join();
    }
    if (message.transactions !== undefined) {
      Transactions.encode(message.transactions, writer.uint32(58).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Account {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAccount();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.maskedAccNumber = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 29) {
            break;
          }

          message.version = reader.float();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.linkedAccRef = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.profile = Profile.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.summary = Summary.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.transactions = Transactions.decode(reader, reader.uint32());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Account {
    return {
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      maskedAccNumber: isSet(object.maskedAccNumber) ? globalThis.String(object.maskedAccNumber) : "",
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      linkedAccRef: isSet(object.linkedAccRef) ? globalThis.String(object.linkedAccRef) : "",
      profile: isSet(object.profile) ? Profile.fromJSON(object.profile) : undefined,
      summary: isSet(object.summary) ? Summary.fromJSON(object.summary) : undefined,
      transactions: isSet(object.transactions) ? Transactions.fromJSON(object.transactions) : undefined,
    };
  },

  toJSON(message: Account): unknown {
    const obj: any = {};
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.maskedAccNumber !== "") {
      obj.maskedAccNumber = message.maskedAccNumber;
    }
    if (message.version !== 0) {
      obj.version = message.version;
    }
    if (message.linkedAccRef !== "") {
      obj.linkedAccRef = message.linkedAccRef;
    }
    if (message.profile !== undefined) {
      obj.profile = Profile.toJSON(message.profile);
    }
    if (message.summary !== undefined) {
      obj.summary = Summary.toJSON(message.summary);
    }
    if (message.transactions !== undefined) {
      obj.transactions = Transactions.toJSON(message.transactions);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Account>, I>>(base?: I): Account {
    return Account.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Account>, I>>(object: I): Account {
    const message = createBaseAccount();
    message.type = object.type ?? "";
    message.maskedAccNumber = object.maskedAccNumber ?? "";
    message.version = object.version ?? 0;
    message.linkedAccRef = object.linkedAccRef ?? "";
    message.profile = (object.profile !== undefined && object.profile !== null)
      ? Profile.fromPartial(object.profile)
      : undefined;
    message.summary = (object.summary !== undefined && object.summary !== null)
      ? Summary.fromPartial(object.summary)
      : undefined;
    message.transactions = (object.transactions !== undefined && object.transactions !== null)
      ? Transactions.fromPartial(object.transactions)
      : undefined;
    return message;
  },
};

function createBaseHolder(): Holder {
  return {
    name: "",
    dob: undefined,
    mobile: "",
    nominee: "",
    landline: "",
    address: "",
    email: "",
    pan: "",
    ckycCompliance: false,
    type: "",
    maskedAccNumber: "",
    linkedAccRef: "",
  };
}

export const Holder: MessageFns<Holder> = {
  encode(message: Holder, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.dob !== undefined) {
      Holder_DateOfBirth.encode(message.dob, writer.uint32(18).fork()).join();
    }
    if (message.mobile !== "") {
      writer.uint32(26).string(message.mobile);
    }
    if (message.nominee !== "") {
      writer.uint32(34).string(message.nominee);
    }
    if (message.landline !== "") {
      writer.uint32(42).string(message.landline);
    }
    if (message.address !== "") {
      writer.uint32(50).string(message.address);
    }
    if (message.email !== "") {
      writer.uint32(58).string(message.email);
    }
    if (message.pan !== "") {
      writer.uint32(66).string(message.pan);
    }
    if (message.ckycCompliance !== false) {
      writer.uint32(72).bool(message.ckycCompliance);
    }
    if (message.type !== "") {
      writer.uint32(82).string(message.type);
    }
    if (message.maskedAccNumber !== "") {
      writer.uint32(90).string(message.maskedAccNumber);
    }
    if (message.linkedAccRef !== "") {
      writer.uint32(98).string(message.linkedAccRef);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Holder {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHolder();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.dob = Holder_DateOfBirth.decode(reader, reader.uint32());
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.mobile = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.nominee = reader.string();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.landline = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.address = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.email = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.pan = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 72) {
            break;
          }

          message.ckycCompliance = reader.bool();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.maskedAccNumber = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.linkedAccRef = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Holder {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      dob: isSet(object.dob) ? Holder_DateOfBirth.fromJSON(object.dob) : undefined,
      mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : "",
      nominee: isSet(object.nominee) ? globalThis.String(object.nominee) : "",
      landline: isSet(object.landline) ? globalThis.String(object.landline) : "",
      address: isSet(object.address) ? globalThis.String(object.address) : "",
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      pan: isSet(object.pan) ? globalThis.String(object.pan) : "",
      ckycCompliance: isSet(object.ckycCompliance) ? globalThis.Boolean(object.ckycCompliance) : false,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      maskedAccNumber: isSet(object.maskedAccNumber) ? globalThis.String(object.maskedAccNumber) : "",
      linkedAccRef: isSet(object.linkedAccRef) ? globalThis.String(object.linkedAccRef) : "",
    };
  },

  toJSON(message: Holder): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.dob !== undefined) {
      obj.dob = Holder_DateOfBirth.toJSON(message.dob);
    }
    if (message.mobile !== "") {
      obj.mobile = message.mobile;
    }
    if (message.nominee !== "") {
      obj.nominee = message.nominee;
    }
    if (message.landline !== "") {
      obj.landline = message.landline;
    }
    if (message.address !== "") {
      obj.address = message.address;
    }
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.pan !== "") {
      obj.pan = message.pan;
    }
    if (message.ckycCompliance !== false) {
      obj.ckycCompliance = message.ckycCompliance;
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.maskedAccNumber !== "") {
      obj.maskedAccNumber = message.maskedAccNumber;
    }
    if (message.linkedAccRef !== "") {
      obj.linkedAccRef = message.linkedAccRef;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Holder>, I>>(base?: I): Holder {
    return Holder.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Holder>, I>>(object: I): Holder {
    const message = createBaseHolder();
    message.name = object.name ?? "";
    message.dob = (object.dob !== undefined && object.dob !== null)
      ? Holder_DateOfBirth.fromPartial(object.dob)
      : undefined;
    message.mobile = object.mobile ?? "";
    message.nominee = object.nominee ?? "";
    message.landline = object.landline ?? "";
    message.address = object.address ?? "";
    message.email = object.email ?? "";
    message.pan = object.pan ?? "";
    message.ckycCompliance = object.ckycCompliance ?? false;
    message.type = object.type ?? "";
    message.maskedAccNumber = object.maskedAccNumber ?? "";
    message.linkedAccRef = object.linkedAccRef ?? "";
    return message;
  },
};

function createBaseHolder_DateOfBirth(): Holder_DateOfBirth {
  return { value: 0 };
}

export const Holder_DateOfBirth: MessageFns<Holder_DateOfBirth> = {
  encode(message: Holder_DateOfBirth, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(8).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Holder_DateOfBirth {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHolder_DateOfBirth();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Holder_DateOfBirth {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 };
  },

  toJSON(message: Holder_DateOfBirth): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Holder_DateOfBirth>, I>>(base?: I): Holder_DateOfBirth {
    return Holder_DateOfBirth.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Holder_DateOfBirth>, I>>(object: I): Holder_DateOfBirth {
    const message = createBaseHolder_DateOfBirth();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseHolders(): Holders {
  return { type: "", holder: [] };
}

export const Holders: MessageFns<Holders> = {
  encode(message: Holders, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== "") {
      writer.uint32(10).string(message.type);
    }
    for (const v of message.holder) {
      Holder.encode(v!, writer.uint32(18).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Holders {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHolders();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.holder.push(Holder.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Holders {
    return {
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      holder: globalThis.Array.isArray(object?.holder) ? object.holder.map((e: any) => Holder.fromJSON(e)) : [],
    };
  },

  toJSON(message: Holders): unknown {
    const obj: any = {};
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.holder?.length) {
      obj.holder = message.holder.map((e) => Holder.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Holders>, I>>(base?: I): Holders {
    return Holders.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Holders>, I>>(object: I): Holders {
    const message = createBaseHolders();
    message.type = object.type ?? "";
    message.holder = object.holder?.map((e) => Holder.fromPartial(e)) || [];
    return message;
  },
};

function createBasePending(): Pending {
  return { transactionType: "", amount: 0 };
}

export const Pending: MessageFns<Pending> = {
  encode(message: Pending, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.transactionType !== "") {
      writer.uint32(10).string(message.transactionType);
    }
    if (message.amount !== 0) {
      writer.uint32(17).double(message.amount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Pending {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePending();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.transactionType = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 17) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Pending {
    return {
      transactionType: isSet(object.transactionType) ? globalThis.String(object.transactionType) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
    };
  },

  toJSON(message: Pending): unknown {
    const obj: any = {};
    if (message.transactionType !== "") {
      obj.transactionType = message.transactionType;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Pending>, I>>(base?: I): Pending {
    return Pending.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Pending>, I>>(object: I): Pending {
    const message = createBasePending();
    message.transactionType = object.transactionType ?? "";
    message.amount = object.amount ?? 0;
    return message;
  },
};

function createBaseSummary(): Summary {
  return {
    currentBalance: "",
    currency: "",
    exchgeRate: "",
    balanceDateTime: undefined,
    type: "",
    branch: "",
    facility: "",
    ifscCode: "",
    micrCode: "",
    openingDate: "",
    currentODLimit: "",
    drawingLimit: "",
    status: "",
    pendingTransactionType: "",
    pendingAmount: 0,
  };
}

export const Summary: MessageFns<Summary> = {
  encode(message: Summary, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.currentBalance !== "") {
      writer.uint32(10).string(message.currentBalance);
    }
    if (message.currency !== "") {
      writer.uint32(18).string(message.currency);
    }
    if (message.exchgeRate !== "") {
      writer.uint32(26).string(message.exchgeRate);
    }
    if (message.balanceDateTime !== undefined) {
      Summary_BalanceDateTime.encode(message.balanceDateTime, writer.uint32(34).fork()).join();
    }
    if (message.type !== "") {
      writer.uint32(42).string(message.type);
    }
    if (message.branch !== "") {
      writer.uint32(50).string(message.branch);
    }
    if (message.facility !== "") {
      writer.uint32(58).string(message.facility);
    }
    if (message.ifscCode !== "") {
      writer.uint32(66).string(message.ifscCode);
    }
    if (message.micrCode !== "") {
      writer.uint32(74).string(message.micrCode);
    }
    if (message.openingDate !== "") {
      writer.uint32(82).string(message.openingDate);
    }
    if (message.currentODLimit !== "") {
      writer.uint32(90).string(message.currentODLimit);
    }
    if (message.drawingLimit !== "") {
      writer.uint32(98).string(message.drawingLimit);
    }
    if (message.status !== "") {
      writer.uint32(106).string(message.status);
    }
    if (message.pendingTransactionType !== "") {
      writer.uint32(114).string(message.pendingTransactionType);
    }
    if (message.pendingAmount !== 0) {
      writer.uint32(121).double(message.pendingAmount);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Summary {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSummary();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.currentBalance = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.currency = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.exchgeRate = reader.string();
          continue;
        }
        case 4: {
          if (tag !== 34) {
            break;
          }

          message.balanceDateTime = Summary_BalanceDateTime.decode(reader, reader.uint32());
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.branch = reader.string();
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.facility = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.ifscCode = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.micrCode = reader.string();
          continue;
        }
        case 10: {
          if (tag !== 82) {
            break;
          }

          message.openingDate = reader.string();
          continue;
        }
        case 11: {
          if (tag !== 90) {
            break;
          }

          message.currentODLimit = reader.string();
          continue;
        }
        case 12: {
          if (tag !== 98) {
            break;
          }

          message.drawingLimit = reader.string();
          continue;
        }
        case 13: {
          if (tag !== 106) {
            break;
          }

          message.status = reader.string();
          continue;
        }
        case 14: {
          if (tag !== 114) {
            break;
          }

          message.pendingTransactionType = reader.string();
          continue;
        }
        case 15: {
          if (tag !== 121) {
            break;
          }

          message.pendingAmount = reader.double();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Summary {
    return {
      currentBalance: isSet(object.currentBalance) ? globalThis.String(object.currentBalance) : "",
      currency: isSet(object.currency) ? globalThis.String(object.currency) : "",
      exchgeRate: isSet(object.exchgeRate) ? globalThis.String(object.exchgeRate) : "",
      balanceDateTime: isSet(object.balanceDateTime)
        ? Summary_BalanceDateTime.fromJSON(object.balanceDateTime)
        : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      branch: isSet(object.branch) ? globalThis.String(object.branch) : "",
      facility: isSet(object.facility) ? globalThis.String(object.facility) : "",
      ifscCode: isSet(object.ifscCode) ? globalThis.String(object.ifscCode) : "",
      micrCode: isSet(object.micrCode) ? globalThis.String(object.micrCode) : "",
      openingDate: isSet(object.openingDate) ? globalThis.String(object.openingDate) : "",
      currentODLimit: isSet(object.currentODLimit) ? globalThis.String(object.currentODLimit) : "",
      drawingLimit: isSet(object.drawingLimit) ? globalThis.String(object.drawingLimit) : "",
      status: isSet(object.status) ? globalThis.String(object.status) : "",
      pendingTransactionType: isSet(object.pendingTransactionType)
        ? globalThis.String(object.pendingTransactionType)
        : "",
      pendingAmount: isSet(object.pendingAmount) ? globalThis.Number(object.pendingAmount) : 0,
    };
  },

  toJSON(message: Summary): unknown {
    const obj: any = {};
    if (message.currentBalance !== "") {
      obj.currentBalance = message.currentBalance;
    }
    if (message.currency !== "") {
      obj.currency = message.currency;
    }
    if (message.exchgeRate !== "") {
      obj.exchgeRate = message.exchgeRate;
    }
    if (message.balanceDateTime !== undefined) {
      obj.balanceDateTime = Summary_BalanceDateTime.toJSON(message.balanceDateTime);
    }
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.branch !== "") {
      obj.branch = message.branch;
    }
    if (message.facility !== "") {
      obj.facility = message.facility;
    }
    if (message.ifscCode !== "") {
      obj.ifscCode = message.ifscCode;
    }
    if (message.micrCode !== "") {
      obj.micrCode = message.micrCode;
    }
    if (message.openingDate !== "") {
      obj.openingDate = message.openingDate;
    }
    if (message.currentODLimit !== "") {
      obj.currentODLimit = message.currentODLimit;
    }
    if (message.drawingLimit !== "") {
      obj.drawingLimit = message.drawingLimit;
    }
    if (message.status !== "") {
      obj.status = message.status;
    }
    if (message.pendingTransactionType !== "") {
      obj.pendingTransactionType = message.pendingTransactionType;
    }
    if (message.pendingAmount !== 0) {
      obj.pendingAmount = message.pendingAmount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Summary>, I>>(base?: I): Summary {
    return Summary.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Summary>, I>>(object: I): Summary {
    const message = createBaseSummary();
    message.currentBalance = object.currentBalance ?? "";
    message.currency = object.currency ?? "";
    message.exchgeRate = object.exchgeRate ?? "";
    message.balanceDateTime = (object.balanceDateTime !== undefined && object.balanceDateTime !== null)
      ? Summary_BalanceDateTime.fromPartial(object.balanceDateTime)
      : undefined;
    message.type = object.type ?? "";
    message.branch = object.branch ?? "";
    message.facility = object.facility ?? "";
    message.ifscCode = object.ifscCode ?? "";
    message.micrCode = object.micrCode ?? "";
    message.openingDate = object.openingDate ?? "";
    message.currentODLimit = object.currentODLimit ?? "";
    message.drawingLimit = object.drawingLimit ?? "";
    message.status = object.status ?? "";
    message.pendingTransactionType = object.pendingTransactionType ?? "";
    message.pendingAmount = object.pendingAmount ?? 0;
    return message;
  },
};

function createBaseSummary_BalanceDateTime(): Summary_BalanceDateTime {
  return { value: 0 };
}

export const Summary_BalanceDateTime: MessageFns<Summary_BalanceDateTime> = {
  encode(message: Summary_BalanceDateTime, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(8).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Summary_BalanceDateTime {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSummary_BalanceDateTime();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Summary_BalanceDateTime {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 };
  },

  toJSON(message: Summary_BalanceDateTime): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Summary_BalanceDateTime>, I>>(base?: I): Summary_BalanceDateTime {
    return Summary_BalanceDateTime.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Summary_BalanceDateTime>, I>>(object: I): Summary_BalanceDateTime {
    const message = createBaseSummary_BalanceDateTime();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTransaction(): Transaction {
  return {
    type: "",
    mode: "",
    amount: 0,
    currentBalance: 0,
    transactionTimestamp: undefined,
    valueDate: undefined,
    txnId: "",
    narration: "",
    reference: "",
  };
}

export const Transaction: MessageFns<Transaction> = {
  encode(message: Transaction, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.type !== "") {
      writer.uint32(10).string(message.type);
    }
    if (message.mode !== "") {
      writer.uint32(18).string(message.mode);
    }
    if (message.amount !== 0) {
      writer.uint32(25).double(message.amount);
    }
    if (message.currentBalance !== 0) {
      writer.uint32(33).double(message.currentBalance);
    }
    if (message.transactionTimestamp !== undefined) {
      Transaction_TransactionTimestamp.encode(message.transactionTimestamp, writer.uint32(42).fork()).join();
    }
    if (message.valueDate !== undefined) {
      Transaction_TransactionTimestamp.encode(message.valueDate, writer.uint32(50).fork()).join();
    }
    if (message.txnId !== "") {
      writer.uint32(58).string(message.txnId);
    }
    if (message.narration !== "") {
      writer.uint32(66).string(message.narration);
    }
    if (message.reference !== "") {
      writer.uint32(74).string(message.reference);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Transaction {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransaction();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.type = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.mode = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 25) {
            break;
          }

          message.amount = reader.double();
          continue;
        }
        case 4: {
          if (tag !== 33) {
            break;
          }

          message.currentBalance = reader.double();
          continue;
        }
        case 5: {
          if (tag !== 42) {
            break;
          }

          message.transactionTimestamp = Transaction_TransactionTimestamp.decode(reader, reader.uint32());
          continue;
        }
        case 6: {
          if (tag !== 50) {
            break;
          }

          message.valueDate = Transaction_TransactionTimestamp.decode(reader, reader.uint32());
          continue;
        }
        case 7: {
          if (tag !== 58) {
            break;
          }

          message.txnId = reader.string();
          continue;
        }
        case 8: {
          if (tag !== 66) {
            break;
          }

          message.narration = reader.string();
          continue;
        }
        case 9: {
          if (tag !== 74) {
            break;
          }

          message.reference = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Transaction {
    return {
      type: isSet(object.type) ? globalThis.String(object.type) : "",
      mode: isSet(object.mode) ? globalThis.String(object.mode) : "",
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currentBalance: isSet(object.currentBalance) ? globalThis.Number(object.currentBalance) : 0,
      transactionTimestamp: isSet(object.transactionTimestamp)
        ? Transaction_TransactionTimestamp.fromJSON(object.transactionTimestamp)
        : undefined,
      valueDate: isSet(object.valueDate) ? Transaction_TransactionTimestamp.fromJSON(object.valueDate) : undefined,
      txnId: isSet(object.txnId) ? globalThis.String(object.txnId) : "",
      narration: isSet(object.narration) ? globalThis.String(object.narration) : "",
      reference: isSet(object.reference) ? globalThis.String(object.reference) : "",
    };
  },

  toJSON(message: Transaction): unknown {
    const obj: any = {};
    if (message.type !== "") {
      obj.type = message.type;
    }
    if (message.mode !== "") {
      obj.mode = message.mode;
    }
    if (message.amount !== 0) {
      obj.amount = message.amount;
    }
    if (message.currentBalance !== 0) {
      obj.currentBalance = message.currentBalance;
    }
    if (message.transactionTimestamp !== undefined) {
      obj.transactionTimestamp = Transaction_TransactionTimestamp.toJSON(message.transactionTimestamp);
    }
    if (message.valueDate !== undefined) {
      obj.valueDate = Transaction_TransactionTimestamp.toJSON(message.valueDate);
    }
    if (message.txnId !== "") {
      obj.txnId = message.txnId;
    }
    if (message.narration !== "") {
      obj.narration = message.narration;
    }
    if (message.reference !== "") {
      obj.reference = message.reference;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Transaction>, I>>(base?: I): Transaction {
    return Transaction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Transaction>, I>>(object: I): Transaction {
    const message = createBaseTransaction();
    message.type = object.type ?? "";
    message.mode = object.mode ?? "";
    message.amount = object.amount ?? 0;
    message.currentBalance = object.currentBalance ?? 0;
    message.transactionTimestamp = (object.transactionTimestamp !== undefined && object.transactionTimestamp !== null)
      ? Transaction_TransactionTimestamp.fromPartial(object.transactionTimestamp)
      : undefined;
    message.valueDate = (object.valueDate !== undefined && object.valueDate !== null)
      ? Transaction_TransactionTimestamp.fromPartial(object.valueDate)
      : undefined;
    message.txnId = object.txnId ?? "";
    message.narration = object.narration ?? "";
    message.reference = object.reference ?? "";
    return message;
  },
};

function createBaseTransaction_TransactionTimestamp(): Transaction_TransactionTimestamp {
  return { value: 0 };
}

export const Transaction_TransactionTimestamp: MessageFns<Transaction_TransactionTimestamp> = {
  encode(message: Transaction_TransactionTimestamp, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.value !== 0) {
      writer.uint32(8).int64(message.value);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Transaction_TransactionTimestamp {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransaction_TransactionTimestamp();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 8) {
            break;
          }

          message.value = longToNumber(reader.int64());
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Transaction_TransactionTimestamp {
    return { value: isSet(object.value) ? globalThis.Number(object.value) : 0 };
  },

  toJSON(message: Transaction_TransactionTimestamp): unknown {
    const obj: any = {};
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Transaction_TransactionTimestamp>, I>>(
    base?: I,
  ): Transaction_TransactionTimestamp {
    return Transaction_TransactionTimestamp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Transaction_TransactionTimestamp>, I>>(
    object: I,
  ): Transaction_TransactionTimestamp {
    const message = createBaseTransaction_TransactionTimestamp();
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseTransactions(): Transactions {
  return { startDate: "", endDate: "", transaction: [] };
}

export const Transactions: MessageFns<Transactions> = {
  encode(message: Transactions, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.startDate !== "") {
      writer.uint32(10).string(message.startDate);
    }
    if (message.endDate !== "") {
      writer.uint32(18).string(message.endDate);
    }
    for (const v of message.transaction) {
      Transaction.encode(v!, writer.uint32(26).fork()).join();
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): Transactions {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTransactions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.startDate = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.endDate = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 26) {
            break;
          }

          message.transaction.push(Transaction.decode(reader, reader.uint32()));
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Transactions {
    return {
      startDate: isSet(object.startDate) ? globalThis.String(object.startDate) : "",
      endDate: isSet(object.endDate) ? globalThis.String(object.endDate) : "",
      transaction: globalThis.Array.isArray(object?.transaction)
        ? object.transaction.map((e: any) => Transaction.fromJSON(e))
        : [],
    };
  },

  toJSON(message: Transactions): unknown {
    const obj: any = {};
    if (message.startDate !== "") {
      obj.startDate = message.startDate;
    }
    if (message.endDate !== "") {
      obj.endDate = message.endDate;
    }
    if (message.transaction?.length) {
      obj.transaction = message.transaction.map((e) => Transaction.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Transactions>, I>>(base?: I): Transactions {
    return Transactions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Transactions>, I>>(object: I): Transactions {
    const message = createBaseTransactions();
    message.startDate = object.startDate ?? "";
    message.endDate = object.endDate ?? "";
    message.transaction = object.transaction?.map((e) => Transaction.fromPartial(e)) || [];
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(int64: { toString(): string }): number {
  const num = globalThis.Number(int64.toString());
  if (num > globalThis.Number.MAX_SAFE_INTEGER) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (num < globalThis.Number.MIN_SAFE_INTEGER) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return num;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
