syntax = "proto3";

package activities;

import "workflows/workflow.proto";
import "common/common.proto";
import "rebit/core.proto";
import "database/core.proto";
import "database/fi_deposit.proto";
import "database/fi_equities.proto";
import "database/fi_mutual_funds.proto";

message FiRequestArgs {
    string consent_id = 1;
    int64 from = 2;
}

message FiRequestActivityResponse {
    string customer_id = 1;
    string consent_id = 2;
    string consent_handle = 3;
    database.FiType fi_type = 4;
    bool is_data_processed_once = 5;
    int64 fi_requested_till = 7;
    string session_id = 8;
    repeated string managed_accounts_linked_acc_ref = 9;
}

message FiStatusArgs {
    string session_id = 1;
}

message FiStatusResponse {
    rebit.SessionStatus status = 1;
    repeated rebit.FiStatusAccount accounts = 2;
}

message FiFetchArgs {
    string session_id = 1;
}

message FiFetchResponse {
    repeated common.FiAccount accounts = 1;
}


message SaveFiAccountsArgs {
    repeated common.FiAccount accounts = 1;
    string consent_id = 2;
    bool is_data_processed_once = 3;
}

message SaveFiAccountsResponse {
}

message MarkConsentDataFetchCompletionArgs {
    string consent_id = 1;
    bool is_data_processed_once = 2;
    int64 fi_processed_till = 3;
    map<string, bool> discovered_accounts = 4;
    map<string, bool> processed_accounts = 5;
}

message MarkConsentDataFetchCompletionResponse {
}

message ProcessTransactionsArgs {
    database.FiType fi_type = 1;
    string consent_id = 2;
}

message ProcessTransactionsResponse {
}

message SaveConsentSessionIdArgs {
    string consent_id = 1;
    string session_id = 2;
}

message SaveConsentSessionIdResponse {
}

message GetLastClosingBalanceByAccountsArgs {
    string consent_id = 1;
}

message GetLastClosingBalanceByAccountsResponse {
    map<string, AccountLastClosingBalance> last_closing_balances = 1;

    message AccountLastClosingBalance {
        database.FiDepositAccountSummary summary = 1;
        database.DepositAccountDailyClosingBalance last_closing_balance = 2;
    }
}

message ProcessDailyClosingBalanceArgs {
    database.FiDepositAccountSummary summary = 1;
    database.DepositAccountDailyClosingBalance last_closing_balance = 2;
}

message ProcessDailyClosingBalanceResponse {
}

message GetEquitiesHoldingsByAccountArgs {
    string consent_id = 1;
}

message GetEquitiesHoldingsByAccountResponse {
    map<string, EquitiesAccountHoldings> holdings = 1;

    message EquitiesAccountHoldings {
        database.FiEquitiesAccountSummary summary = 1;
        repeated database.EquitiesGain holdings = 2;
    } 
}

message ProcessEquitiesGainsArgs {
    database.FiEquitiesAccountSummary summary = 1;
    repeated database.EquitiesGain holdings = 2;
}

message ProcessEquitiesGainsResponse {
}

message GetMutualFundsHoldingsByAccountArgs {
    string consent_id = 1;
}

message GetMutualFundsHoldingsByAccountResponse {
    map<string, MutualFundsAccountHoldings> holdings = 1;

    message MutualFundsAccountHoldings {
        database.FiMutualFundsAccountSummary summary = 1;
        repeated database.MutualFundsGain holdings = 2;
    } 
}

message ProcessMutualFundsGainsArgs {
    database.FiMutualFundsAccountSummary summary = 1;
    repeated database.MutualFundsGain holdings = 2;
}

message ProcessMutualFundsGainsResponse {
}

message GetConsentManagedAccountIdsArgs {
    string consent_id = 1;
    database.FiType fi_type = 2;
}

message GetConsentManagedAccountIdsResponse {
    repeated string ids = 1; 
}

message AnalyseDepositAccountTransactionsArgs {
    string account_id = 1;
}

message AnalyseDepositAccountTransactionsResponse {
}

message InvokeFiProcessWorkflowArgs {
    string workflow_id = 1;
    workflows.FiProcessArgs args = 2;
}

message InvokeFiProcessWorkflowRes {
}