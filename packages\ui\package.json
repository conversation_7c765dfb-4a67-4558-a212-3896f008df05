{"name": "@repo/ui", "version": "0.0.0", "exports": {".": "./src/index.tsx", "./icons": "./src/icons/index.ts", "./icons/categories": "./src/icons/categories/index.ts", "./icons/bank": "./src/icons/bank/index.ts", "./data/dummyNavigation": "./src/data/dummyNavigation.ts", "./styles.css": "./src/styles.css"}, "license": "MIT", "scripts": {"lint": "eslint src/ --max-warnings 0", "type-check": "tsc --noEmit", "generate-icons": "svgr -d src/icons/ assets/ --icon --typescript --filename-case kebab"}, "dependencies": {"@fontsource-variable/plus-jakarta-sans": "^5.1.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "lucide-react": "^0.468.0", "react-day-picker": "8.10.1", "tailwind-merge": "^2.5.5"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@svgr/cli": "^8.1.0", "@types/node": "^22.10.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}