import { SelectedIcon } from "../icons/selected";
interface IconButtonWithTooltipProps {
  onClick?: () => void;
  active: boolean;
  tooltipMessage: string;
  svgIcon: React.ReactNode;
  label: string;
}

const MoreFilterButton: React.FC<IconButtonWithTooltipProps> = ({
  onClick,
  active,
  svgIcon,
  label,
}) => {
  return (
    <div className="flex flex-col">
      <div
        className={` w-12 h-12 p-[10px] rounded-lg flex items-center justify-center mb-1 relative ${
          active ? "bg-[#905BB5] " : "bg-[#FAF8FC] "
        }`}
        onClick={onClick}
      >
        <span className="text-xl">{svgIcon}</span>
        {active && (
          <SelectedIcon className="text-[#905BB5] absolute top-0 right-0 translate-x-[40%] -translate-y-[40%] w-5 h-5 rounded-full flex items-center justify-center z-10" />
        )}
      </div>
      <span className="text-xs font-medium block w-24">{label}</span>
    </div>
  );
};
interface FilterOptionsProps {
  filters: {
    showFavorites: boolean;
    withNotes: boolean;
    excludedFromCashflow: boolean;
  };
  onFilterChange: (filterKey: string, value: boolean) => void;
  untaggedCount?: number;
}

export const BookmarkFilterOptions: React.FC<FilterOptionsProps> = ({
  filters,
  onFilterChange,
}) => {

  return (
    <div>
      <p className="  text-sm leading-10 ">More Filters</p>
      <div className="flex  gap-x-[34px] gap-y-[30px] py-5 mr-4">
        {/* Bookmarked */}
        <MoreFilterButton
          onClick={() =>
            onFilterChange("showFavorites", !filters.showFavorites)
          }
          active={filters.showFavorites}
          tooltipMessage="Bookmarked Transactions"
          svgIcon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 12 14"
              fill="none"
            >
              <path
                d="M10.6877 13.0265L6.00009 9.67819L1.3125 13.0265V2.31197C1.3125 1.95676 1.45361 1.6161 1.70478 1.36493C1.95595 1.11376 2.2966 0.972656 2.65181 0.972656H9.34838C9.70358 0.972656 10.0442 1.11376 10.2954 1.36493C10.5466 1.6161 10.6877 1.95676 10.6877 2.31197V13.0265Z"
                stroke="#C4A4DC"
                strokeWidth="1.7"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          }
          label="Favourited"
        />

        {/* Excluded */}
        <MoreFilterButton
          onClick={() => onFilterChange("withNotes", !filters.withNotes)}
          active={filters.withNotes}
          tooltipMessage="Excluded from Cashflow"
          svgIcon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M2.89989 19.2062C3.17571 19.3402 3.48356 19.3922 3.78749 19.356C4.09143 19.3198 4.37892 19.197 4.61639 19.0019C4.75726 18.876 4.94064 18.8098 5.12851 18.817C5.45041 18.817 5.58575 18.9316 5.87473 19.2154C6.08481 19.4616 6.34492 19.6591 6.63734 19.7945C6.92977 19.9299 7.24764 20 7.56928 20C7.89093 20 8.2088 19.9299 8.50122 19.7945C8.79365 19.6591 9.05376 19.4616 9.26383 19.2154C9.55647 18.9316 9.68724 18.817 10.0082 18.817C10.3292 18.817 10.46 18.9316 10.7517 19.2154C10.9615 19.4611 11.2212 19.6581 11.5132 19.7932C11.8051 19.9283 12.1224 19.9983 12.4435 19.9983C12.7646 19.9983 13.0819 19.9283 13.3738 19.7932C13.6658 19.6581 13.9255 19.4611 14.1353 19.2154C14.428 18.9316 14.5587 18.817 14.8797 18.817C15.0637 18.8095 15.2437 18.8733 15.3827 18.9954C15.6211 19.1903 15.9093 19.3127 16.2138 19.3484C16.5184 19.3841 16.8267 19.3315 17.1028 19.1969C17.3731 19.0666 17.601 18.8611 17.7599 18.6046C17.9188 18.3481 18.002 18.0511 18 17.7485V1.61761C17.9995 1.18938 17.8312 0.778781 17.532 0.475631C17.2328 0.172481 16.8269 0.00146626 16.4033 0H3.60039C3.1761 0.000489296 2.76933 0.171073 2.4693 0.474328C2.16928 0.777584 2.00052 1.18875 2.00004 1.61761V17.7549C1.99802 18.0583 2.0816 18.3559 2.24097 18.6129C2.40035 18.87 2.62893 19.0758 2.89989 19.2062ZM8.63009 3.6974H13.6598C13.8417 3.6974 14.0161 3.77044 14.1448 3.90045C14.2734 4.03047 14.3457 4.2068 14.3457 4.39067C14.3457 4.57453 14.2734 4.75086 14.1448 4.88088C14.0161 5.01089 13.8417 5.08393 13.6598 5.08393H8.63009C8.44819 5.08393 8.27374 5.01089 8.14511 4.88088C8.01649 4.75086 7.94422 4.57453 7.94422 4.39067C7.94422 4.2068 8.01649 4.03047 8.14511 3.90045C8.27374 3.77044 8.44819 3.6974 8.63009 3.6974ZM8.63009 8.31915H13.6598C13.8417 8.31915 14.0161 8.39219 14.1448 8.52221C14.2734 8.65222 14.3457 8.82855 14.3457 9.01242C14.3457 9.19628 14.2734 9.37262 14.1448 9.50263C14.0161 9.63264 13.8417 9.70568 13.6598 9.70568H8.63009C8.44819 9.70568 8.27374 9.63264 8.14511 9.50263C8.01649 9.37262 7.94422 9.19628 7.94422 9.01242C7.94422 8.82855 8.01649 8.65222 8.14511 8.52221C8.27374 8.39219 8.44819 8.31915 8.63009 8.31915ZM8.63009 12.9409H13.6598C13.8417 12.9409 14.0161 13.0139 14.1448 13.144C14.2734 13.274 14.3457 13.4503 14.3457 13.6342C14.3457 13.818 14.2734 13.9944 14.1448 14.1244C14.0161 14.2544 13.8417 14.3274 13.6598 14.3274H8.63009C8.44819 14.3274 8.27374 14.2544 8.14511 14.1244C8.01649 13.9944 7.94422 13.818 7.94422 13.6342C7.94422 13.4503 8.01649 13.274 8.14511 13.144C8.27374 13.0139 8.44819 12.9409 8.63009 12.9409ZM6.34387 3.46631C6.52473 3.46631 6.70154 3.52053 6.85193 3.6221C7.00232 3.72366 7.11953 3.86803 7.18875 4.03693C7.25796 4.20583 7.27607 4.39169 7.24078 4.571C7.2055 4.7503 7.1184 4.91501 6.99051 5.04428C6.86261 5.17355 6.69967 5.26159 6.52227 5.29725C6.34488 5.33292 6.16101 5.31462 5.99391 5.24465C5.8268 5.17469 5.68398 5.05622 5.58349 4.90421C5.48301 4.7522 5.42938 4.57348 5.42938 4.39067C5.42938 4.14551 5.52572 3.9104 5.69722 3.73705C5.86872 3.5637 6.10133 3.46631 6.34387 3.46631ZM6.34387 8.08807C6.52473 8.08807 6.70154 8.14228 6.85193 8.24385C7.00232 8.34542 7.11953 8.48978 7.18875 8.65868C7.25796 8.82759 7.27607 9.01344 7.24078 9.19275C7.2055 9.37206 7.1184 9.53676 6.99051 9.66603C6.86261 9.7953 6.69967 9.88334 6.52227 9.91901C6.34488 9.95467 6.16101 9.93637 5.99391 9.86641C5.8268 9.79644 5.68398 9.67797 5.58349 9.52596C5.48301 9.37395 5.42938 9.19524 5.42938 9.01242C5.42938 8.76726 5.52572 8.53215 5.69722 8.3588C5.86872 8.18545 6.10133 8.08807 6.34387 8.08807ZM6.34387 12.7098C6.52473 12.7098 6.70154 12.764 6.85193 12.8656C7.00232 12.9672 7.11953 13.1115 7.18875 13.2804C7.25796 13.4493 7.27607 13.6352 7.24078 13.8145C7.2055 13.9938 7.1184 14.1585 6.99051 14.2878C6.86261 14.4171 6.69967 14.5051 6.52227 14.5408C6.34488 14.5764 6.16101 14.5581 5.99391 14.4882C5.8268 14.4182 5.68398 14.2997 5.58349 14.1477C5.48301 13.9957 5.42938 13.817 5.42938 13.6342C5.42938 13.389 5.52572 13.1539 5.69722 12.9806C5.86872 12.8072 6.10133 12.7098 6.34387 12.7098Z"
                fill="#C4A4DC"
              />
            </svg>
          }
          label="With notes"
        />
        <MoreFilterButton
          onClick={() =>
            onFilterChange(
              "excludedFromCashflow",
              !filters.excludedFromCashflow
            )
          }
          active={filters.excludedFromCashflow}
          tooltipMessage="Excluded from Cashflow"
          svgIcon={
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 20 16"
              fill="none"
            >
              <path
                stroke="#C4A4DC"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.43025 3.47198C8.18008 3.65044 7.06004 4.34293 6.34331 5.38396C5.6276 6.425 5.37849 7.71793 5.65828 8.94942C5.93899 10.1819 6.72163 11.2406 7.81749 11.8691H5.2381C4.2956 10.7016 3.80761 9.234 3.86151 7.73468C3.91542 6.2354 4.50845 4.80669 5.53088 3.70903C6.55426 2.61222 7.93918 1.92161 9.43018 1.76359V3.48131L9.43025 3.47198ZM10.1469 12.4711H2.80849V11.669C2.80849 11.5277 2.72576 11.4004 2.59656 11.3418C2.46736 11.2842 2.31678 11.3065 2.21082 11.4004L0.307216 13.066C0.22914 13.1339 0.183594 13.2333 0.183594 13.3374C0.183594 13.4415 0.22914 13.54 0.307216 13.6088L2.21082 15.2745C2.31678 15.3684 2.46828 15.3907 2.59749 15.3321C2.72669 15.2736 2.80942 15.1443 2.80849 15.0031V14.1795H10.1469C12.3721 14.1702 14.4236 12.9731 15.5277 11.0415C16.6319 9.10903 16.6217 6.73407 15.5007 4.81095C14.3798 2.88876 12.3172 1.71021 10.0921 1.72044H10.028V3.42886H10.0921C11.7076 3.42049 13.204 4.2747 14.0192 5.66988C14.8335 7.06505 14.8418 8.78822 14.0415 10.1909C13.2403 11.5935 11.7522 12.4626 10.1377 12.471L10.1469 12.4711ZM12.9214 1.716H17.3756V0.916627C17.3747 0.774413 17.4574 0.646148 17.5866 0.58666C17.7158 0.528103 17.8673 0.551339 17.9733 0.645218L19.8769 2.31087C19.9549 2.37873 20.0005 2.47818 20.0005 2.58228C20.0005 2.68638 19.9549 2.78491 19.8769 2.85276L17.9733 4.51841C17.8673 4.61229 17.7167 4.63553 17.5875 4.57697C17.4583 4.51934 17.3756 4.39107 17.3756 4.24979V3.42905H15.234C14.5927 2.70218 13.8045 2.11939 12.9214 1.72063L12.9214 1.716Z"
              />
            </svg>
          }
          label="Excluded from cashflow"
        />
      </div>
    </div>
  );
};
