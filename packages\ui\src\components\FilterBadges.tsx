import { X } from "lucide-react";
import { Badge } from "@repo/ui";
import { CalendarSvgIcon } from "../icons/calendar";

interface FilterBadgesProps {
  badges: string[];
  onRemoveFilter: (filterType: string | undefined, value?: string) => void;
}

const FilterBadges: React.FC<FilterBadgesProps> = ({
  badges,
  onRemoveFilter,
}) => {
  if (!badges || badges.length === 0) return null;

  const badgeStyle =
    "flex overflow-hidden w-fit items-center bg-[#FBFBFB] text-black text-xs font-medium px-4 py-2 border border-[#EBEBEB] rounded-full";

  return (
    <div className="flex flex-wrap mb-3 gap-3 items-center">
      {badges.map((badge, index) => {
        const isDate =
          (/\d{1,2}\s-\s\w{3}/i.test(badge) ||
            /\d{1,2}\/\d{1,2}\/\d{4}/.test(badge)) &&
          !badge.includes("₹");
        const isCategory = badge.includes(": ");

        let categoryName = "";

        if (isCategory) {
          categoryName = badge.split(": ")[0]!;
        }

        return (
          <div className="flex" key={`${badge}-${index}`}>
            {isDate && (
              <div className="rounded-full p-2 mr-2 bg-[#905BB5] text-center text-white">
                <CalendarSvgIcon className="w-4 h-4" />
              </div>
            )}
            <Badge variant="default" className={badgeStyle} selected={false}>
              <div className="flex items-center truncate gap-3">
                <span>{badge}</span>
                <button
                  type="button"
                  onClick={() => onRemoveFilter(badge)}
                  className="ml-2"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </Badge>
          </div>
        );
      })}
    </div>
  );
};

export { FilterBadges };
