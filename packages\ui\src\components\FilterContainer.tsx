"use client";
import { useEffect, useState } from "react";
import { DateRange } from "react-day-picker";
import {
  Group,
  Member,
  MemberGroupFilter,
  SelectedAccountsType,
} from "./MemberGroupFilter";
import { Separator } from "./ui/separator";
import { DateFilter } from "./DateFilter";
import { BookmarkFilterOptions } from "./BookmarkFilterOptions";
import { format } from "date-fns";
import { AmountRangeFilter } from "./AmountRangeFilter";
import { MoreFilter } from "./MoreFilter";
import { CategoryFilter } from "./CategoryFilter";
import { Button } from "./ui/button";
import { debitCategories } from "../data/dummyTransactions";

export const defaultBookmarkOptions = {
  showFavorites: false,
  excludedFromCashflow: false,
  withNotes: false,
};

export interface AppliedFilters {
  selectedTags: string[];
  selectedRange: DateRange | undefined;
  selectedAmountRange: number[];
  selectedDateLabel: string;
  selectedMembers: string[];
  selectedGroups: string[];
  selectedAccounts: SelectedAccountsType;
  selectedBookmarkOptions: typeof defaultBookmarkOptions;
  selectedTransactionType: string;
  selectedTagStatus: string;
  selectedCategories: Record<string, string[]>;
  categoryFilters?: Array<{
    categoryCollection: string;
    categoryId: string;
    subcategoryId: string;
  }>;
}

interface FilterProps {
  onApply: (filters: AppliedFilters) => void;
  onClear: (filterType: string | undefined, value?: string) => void;
  filters?: AppliedFilters;
  onFiltersChange?: (filters: AppliedFilters) => void;
  onClose?: () => void;
  members: Member[];
  groups: Group[];
}

export const initialFilters: AppliedFilters = {
  selectedTags: [],
  selectedRange: undefined,
  selectedAmountRange: [0, 200000],
  selectedDateLabel: "All time",
  selectedMembers: [],
  selectedGroups: [],
  selectedAccounts: { accountIds: new Set() },
  selectedBookmarkOptions: defaultBookmarkOptions,
  selectedTransactionType: "All",
  selectedTagStatus: "All",
  selectedCategories: {},
};
const syncSelectedCategoriesToFilters = (
  selectedCategories: Record<string, string[]>
): Array<{
  categoryCollection: string;
  categoryId: string;
  subcategoryId: string;
}> => {
  const categoryFilters: Array<{
    categoryCollection: string;
    categoryId: string;
    subcategoryId: string;
  }> = [];

  Object.entries(selectedCategories).forEach(([categoryId, subcategoryIds]) => {
    subcategoryIds.forEach((subcategoryId) => {
      categoryFilters.push({
        categoryCollection: "global",
        categoryId: categoryId,
        subcategoryId: subcategoryId,
      });
    });
  });

  return categoryFilters;
};
export const FilterContainer = ({
  onApply,
  onClear,
  filters,
  onFiltersChange,
  onClose,
  members,
  groups,
}: FilterProps) => {
  const [currentFilters, setCurrentFilters] = useState<AppliedFilters>({
    ...initialFilters,
  });

  useEffect(() => {
    if (filters) {
      const updatedFilters = {
        ...filters,
        selectedCategories: JSON.parse(
          JSON.stringify(filters.selectedCategories || {})
        ),
      };
      if (
        !updatedFilters.categoryFilters &&
        Object.keys(updatedFilters.selectedCategories).length > 0
      ) {
        updatedFilters.categoryFilters = syncSelectedCategoriesToFilters(
          updatedFilters.selectedCategories
        );
      }
      setCurrentFilters(updatedFilters);
    }
  }, [filters]);

  useEffect(() => {
    onFiltersChange?.(currentFilters);
  }, [currentFilters, onFiltersChange]);

  const handleApply = () => {
    const filtersToApply = {
      ...currentFilters,
      categoryFilters: syncSelectedCategoriesToFilters(
        currentFilters.selectedCategories
      ),
    };
    onApply(filtersToApply);
    if (onClose) {
      onClose();
    }
  };

  const handleClearAll = () => {
    setCurrentFilters({
      ...initialFilters,
      selectedCategories: {},
      categoryFilters: [],
    });
    onClear("ALL", "ALL");
    onApply({
      ...initialFilters,
      selectedCategories: {},
      categoryFilters: [],
    });
    if (onClose) {
      onClose();
    }
  };

  return (
    <div className="flex flex-col h-full mt-6">
      <div className="space-y-6 flex-1 overflow-y-auto overflow-x-hidden pb-6">
        <div className="px-4 ">
          <MemberGroupFilter
            selectedMembers={currentFilters.selectedMembers}
            selectedGroups={currentFilters.selectedGroups}
            members={members}
            groups={groups}
            selectedAccounts={currentFilters.selectedAccounts}
            onSelectionChange={(
              selectedMemberList,
              selectedGroupList,
              updatedAccounts
            ) => {
              setTimeout(() => {
                setCurrentFilters((prev) => ({
                  ...prev,
                  selectedAccounts: updatedAccounts,
                  selectedMembers: selectedMemberList.map((m) => m.memberId),
                  selectedGroups: selectedGroupList.map((g) => g.groupId),
                }));
              }, 0);
            }}
          />
        </div>
        <Separator className="bg-[#F6F6F6] h-2 w-full" />
        <div className="px-4 ">
          <DateFilter
            initialRange={currentFilters.selectedRange}
            onFilterChange={({ startDate, endDate, range }) => {
              setCurrentFilters((prev) => ({
                ...prev,
                selectedDateLabel:
                  startDate && endDate
                    ? `${format(new Date(startDate), "MMM d")} - ${format(
                        new Date(endDate),
                        "MMM d, yyyy"
                      )}`
                    : "Select date",
                selectedRange: range,
              }));
            }}
            onCalendarChange={(range) =>
              setCurrentFilters((prev) => ({
                ...prev,
                selectedRange: range,
              }))
            }
          />
        </div>
        <Separator className="bg-[#F6F6F6] h-2 w-full" />
        <div className="px-4 ">
          <MoreFilter
            label="Transaction Type"
            options={["All", "Incoming", "Outgoing"]}
            selected={currentFilters.selectedTransactionType}
            onSelect={(value) =>
              setCurrentFilters((prev) => ({
                ...prev,
                selectedTransactionType: value,
              }))
            }
          />
        </div>
        <Separator className="bg-[#F6F6F6] h-2 w-full" />
        <div className="px-4 ">
          <MoreFilter
            label="Tag Status"
            options={["All", "Tagged", "Untagged"]}
            selected={currentFilters.selectedTagStatus}
            onSelect={(value) =>
              setCurrentFilters((prev) => ({
                ...prev,
                selectedTagStatus: value,
              }))
            }
          />
        </div>
        <Separator className="bg-[#F6F6F6] h-2 w-full" />
        <div className="px-4 pt-2">
          <CategoryFilter
            categories={debitCategories}
            selectedCategories={currentFilters.selectedCategories}
            setSelectedCategories={(newSelectedCategories) => {
              setCurrentFilters((prevFilters) => {
                const updatedCategories =
                  newSelectedCategories instanceof Function
                    ? newSelectedCategories(prevFilters.selectedCategories)
                    : newSelectedCategories;
                return {
                  ...prevFilters,
                  selectedCategories: updatedCategories,
                  categoryFilters:
                    syncSelectedCategoriesToFilters(updatedCategories),
                };
              });
            }}
          />
        </div>
        <Separator className="bg-[#F6F6F6] h-2 w-full" />
        <div className="px-4 ">
          <AmountRangeFilter
            values={currentFilters.selectedAmountRange}
            onValueChange={(values) => {
              setCurrentFilters((prev) => ({
                ...prev,
                selectedAmountRange: values as number[],
              }));
            }}
            min={0}
            max={200000}
            step={100}
          />
        </div>
        <Separator className="bg-[#F6F6F6] h-2 w-full" />
        <div className="px-4">
          <BookmarkFilterOptions
            filters={currentFilters.selectedBookmarkOptions}
            onFilterChange={(option, checked) =>
              setCurrentFilters((prev) => ({
                ...prev,
                selectedBookmarkOptions: {
                  ...prev.selectedBookmarkOptions,
                  [option]: checked,
                },
              }))
            }
          />
        </div>
      </div>

      <div className="border-t px-4 py-4 bg-white sticky bottom-0 z-10 flex justify-between">
        <Button
          variant="secondary"
          className="w-[154px] px-6 py-3.5 text-black"
          onClick={handleClearAll}
        >
          Clear all
        </Button>
        <Button
          variant="default"
          className="w-[154px] px-6 py-3.5"
          onClick={handleApply}
        >
          Show
        </Button>
      </div>
    </div>
  );
};
