"use client";
import React, { useEffect, useMemo, useState } from "react";
import { cn } from "../lib/utils";
import { categoryIconMap, TransactionMultiSelectCategory } from "./AddEditTag";
import { Input } from "./ui/input";
import { SelectedIcon } from "../icons/selected";
import { Category } from "./TransactionsTable";
import SvgSearch from "../icons/search";
import { Misc } from "../icons/categories";

interface CategorySelectorProps {
  categories: Category[];
  selectedCategories: TransactionMultiSelectCategory | TransactionMultiSelectCategory[] | null;
  onCategorySelectionChange: (selection: TransactionMultiSelectCategory[] | TransactionMultiSelectCategory | null) => void;
  mode: "modal" | "filter"
  multiSelection: boolean;
  showEveryCategories: boolean;
}

export const CategorySelector: React.FC<CategorySelectorProps> = ({
  categories,
  selectedCategories,
  onCategorySelectionChange,
  mode = "modal",
  multiSelection,
  showEveryCategories
}) => {
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [showAllCategories, setShowAllCategories] = useState(showEveryCategories);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const CATEGORIES_PER_ROW = 4;

  let selectedCategoryIds = useMemo(() => {
    if (!selectedCategories) return [];
    const list = Array.isArray(selectedCategories)
      ? selectedCategories
      : [selectedCategories];
    return list.map((sc) => sc.category_id);
  }, [selectedCategories]);

  const selectedSubcategories = useMemo(() => {
    if (!selectedCategories) return {};
    const list = Array.isArray(selectedCategories)
      ? selectedCategories
      : [selectedCategories];
    return list.reduce((acc, sc) => {
      acc[sc.category_id] = sc.subcategory_id;
      return acc;
    }, {} as { [categoryId: string]: string[] });
  }, [selectedCategories]);

  const categoryRefs = useMemo(() => {
    return categories.reduce<{
      [categoryId: string]: React.RefObject<HTMLButtonElement>;
    }>((acc, category) => {
      acc[category.id] = { current: null };
      return acc;
    }, {});
  }, [categories]);
  const hasSelectedSubcategories = (categoryId: string): boolean => {
    return (
      selectedSubcategories[categoryId] !== undefined &&
      selectedSubcategories[categoryId].length > 0
    );
  };
  const onSubcategoryToggle = (
    categoryId: string,
    subId: string
  ) => {
    let updated: string[];
    let newState: {}
    if (multiSelection) {
      if (subId === "__SELECT_ALL__") {
        // Select All
        updated = categories.find((cat) => cat.id === categoryId)?.subCategories.map((cat) => cat.id) ?? [];
      } else if (subId === "__CLEAR_ALL__") {
        updated = [];
      } else {
        const current = selectedSubcategories[categoryId] || [];
        updated = current.includes(subId)
          ? current.filter((i) => i !== subId)
          : [...current, subId];
      }
      newState = {
        ...selectedSubcategories,
        [categoryId]: updated,
      };
      const selectedArray: TransactionMultiSelectCategory[] = Object.entries(newState)
        .filter(([_, subIds]) => (subIds as string[]).length > 0)
        .map(([catId, subIds]) => ({
          category_id: catId,
          subcategory_id: subIds as string[],
        }));
      onCategorySelectionChange(selectedArray.length > 0 ? selectedArray : null);
    } else {
      onCategorySelectionChange({
        category_id: categoryId,
        subcategory_id: [subId],
      });
    }
  };

  const filteredCategories = useMemo(() => {
    if (!searchQuery.trim()) return categories;

    const lowerQuery = searchQuery.toLowerCase();
    return categories.filter((cat) => {
      const categoryNameMatch = cat.name.toLowerCase().includes(lowerQuery);
      const subCategoryMatch = cat.subCategories?.some((sub) =>
        sub.name.toLowerCase().includes(lowerQuery)
      );
      return categoryNameMatch || subCategoryMatch;
    });
  }, [searchQuery, categories]);

  const getCategoryRows = (list: Category[]) => {
    const rows = [];
    for (let i = 0; i < list.length; i += CATEGORIES_PER_ROW) {
      rows.push(list.slice(i, i + CATEGORIES_PER_ROW));
    }
    return rows;
  };

  const categoryRows = getCategoryRows(filteredCategories);

  return (
    <div className={cn("h-full flex flex-col items-stretch", mode === "filter" && "mb-1")}>
      <div
        className={`${mode === "modal" ? "bg-[linear-gradient(0deg,_#FFF_63.25%,_#FAF8FC_100%)] border border-[#EBE0F4] rounded-lg px-[14px] py-[13px] mt-[15px]" : ""}`}
      >
        {mode === "modal" && (
          <p className="font-normal text-sm mb-[5px] mt-1">Main-tag name</p>
        )}

        <div className="relative mb-[20px]">
          <Input
            type="text"
            placeholder={
              mode === "modal" ? "Try 'Movie tickets'" : "Search for tags"
            }
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
          <div className="absolute inset-y-0 left-3 flex items-center">
            <span>
              <SvgSearch />
            </span>
          </div>
        </div>
      </div>

      <div className="overflow-hidden h-full mt-6 px-[20px]">
        {filteredCategories.length > 0 ? (
          <>
            {(showAllCategories ? categoryRows : categoryRows.slice(0, 4)).map(
              (row, rowIndex) => {
                return (
                  <div key={rowIndex} className="flex flex-col gap-x-2 mb-4">
                    <div className="grid grid-cols-4 gap-x-4">
                      {row.map((category, colIndex) => {
                        const isActive = activeCategory === category.id;
                        const hasActiveSelections = hasSelectedSubcategories(category.id);
                        const subcategoryNames =
                          category.subCategories?.map((s) => s.name) || [];
                        const currentSelectedSub =
                          selectedSubcategories[category.id] || [];
                        const selectedCount = currentSelectedSub.length;
                        const allSelected =
                          selectedCount === subcategoryNames.length &&
                          subcategoryNames.length > 0;

                        return (
                          <div
                            key={category.id}
                            className="flex flex-col items-center w-full"
                          >
                            <button
                              ref={categoryRefs[category.id]}
                              onClick={() => {
                                setActiveCategory(category.id);
                                if (multiSelection) {
                                  const isSelected = selectedCategoryIds.includes(category.id);
                                  let newSubcategoryState = { ...selectedSubcategories };

                                  if (isSelected) {
                                    selectedCategoryIds = selectedCategoryIds.filter((item) => item !== category.id);
                                  } else {
                                    newSubcategoryState[category.id] = [];
                                  }

                                  const selectedArray: TransactionMultiSelectCategory[] = Object.entries(newSubcategoryState)
                                    .filter(([_, subIds]) => (subIds as string[]).length > 0)
                                    .map(([catId, subIds]) => ({
                                      category_id: catId,
                                      subcategory_id: subIds as string[],
                                    }));
                                  onCategorySelectionChange(selectedArray.length > 0 ? selectedArray : null);
                                } else {
                                  const selectedSub = selectedSubcategories?.[category.id];
                                  const isValidSub = selectedSub && selectedSub.length > 0 && selectedSub[0] !== '';
                                  const others = category.subCategories.find((s) => s.name === "Others");
                                  const defaultSub = others || category.subCategories[0];
                                  if (isValidSub) {
                                    onCategorySelectionChange({
                                      category_id: category.id,
                                      subcategory_id: selectedSub,
                                    });
                                  } else if (defaultSub) {
                                    onCategorySelectionChange({
                                      category_id: category.id,
                                      subcategory_id: [defaultSub.id],
                                    });
                                  } else {
                                    onCategorySelectionChange(null);
                                  }
                                }
                              }}
                              className={cn(
                                "flex flex-col relative justify-start items-center w-full h-full pt-3 pb-1",
                                isActive
                                  ? cn(
                                    "bg-[#F4EEF9] rounded-t-2xl",
                                    colIndex !== 0 &&
                                    colIndex !== row.length - 1 &&
                                    "rounded-out-b-2xl",
                                    colIndex === 0 && "rounded-out-br-2xl",
                                    colIndex === row.length - 1 &&
                                    "rounded-out-bl-2xl "
                                  )
                                  : "bg-transparent"
                              )}
                            >
                              <div
                                className={cn(
                                  "relative w-14 h-14 rounded-lg flex items-center justify-center",
                                  isActive || hasActiveSelections
                                    ? "bg-[#905BB5]" : "bg-[#FAF8FC]"
                                )}
                              >
                                {hasActiveSelections && (
                                  <div>
                                    {allSelected ? (
                                      <SelectedIcon className="text-[#905BB5] absolute top-0 right-0 translate-x-[40%] -translate-y-[40%] w-5 h-5 rounded-full flex items-center justify-center z-10" />
                                    ) : (
                                      <svg
                                        width="21"
                                        height="21"
                                        viewBox="0 0 21 21"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="text-[#905BB5] absolute top-0 right-0 translate-x-[40%] -translate-y-[40%] w-5 h-5 rounded-full flex items-center justify-center z-10"
                                      >
                                        <g id="Group 1000002379">
                                          <rect
                                            x="1.28947"
                                            y="1.28947"
                                            width="18.4211"
                                            height="18.4211"
                                            rx="9.21053"
                                            fill="#A7C4FE"
                                            stroke="white"
                                            strokeWidth="1.57895"
                                          />
                                          <path
                                            stroke="#FBFBFB"
                                            strokeWidth="1.57895"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          />
                                          {selectedCount !== undefined && (
                                            <text
                                              x="10.5"
                                              y="14"
                                              textAnchor="middle"
                                              fill="white"
                                              fontSize="10"
                                              fontWeight="medium"
                                            >
                                              {selectedCount}
                                            </text>
                                          )}
                                        </g>
                                      </svg>
                                    )}
                                  </div>
                                )}
                                <span
                                  className={cn(
                                    "text-xl",
                                    isActive || hasActiveSelections
                                      ? "text-white"
                                      : "text-[#C4A4DC]"
                                  )}
                                >
                                  {categoryIconMap[category.name] || <Misc />}
                                </span>
                              </div>
                              <span className="text-xs font-medium p-2 text-center">
                                {category.name}
                              </span>
                            </button>
                          </div>
                        );
                      })}
                    </div>

                    {/* Subcategory Panel */}
                    {activeCategory &&
                      row.some((cat) => cat.id === activeCategory) && (
                        <div
                          className={cn(
                            "flex flex-wrap bg-[#F4EEF9] py-4 px-4 rounded-xl w-full mx-auto",
                            row.findIndex(
                              (cat) => cat.id === activeCategory
                            ) === 0 && "rounded-tl-none",
                            row.findIndex(
                              (cat) => cat.id === activeCategory
                            ) ===
                            row.length - 1 && "rounded-tr-none"
                          )}
                        >
                          {(() => {
                            const activeCat = row.find(
                              (cat) => cat.id === activeCategory
                            );
                            if (!activeCat) return null;
                            let selectedList =
                              selectedSubcategories[activeCat.id] || [];

                            const allSub =
                              activeCat.subCategories || [];

                            const isAllSelected =
                              allSub.length > 0 &&
                              selectedList.length === allSub.length;

                            const actionButton =
                              multiSelection
                                ? isAllSelected
                                  ? "Clear All"
                                  : "Select All"
                                : null;

                            const itemsToRender = actionButton ? [actionButton, ...allSub] : allSub;

                            return itemsToRender.map((sub) => {
                              const isAction = typeof sub === "string";
                              const subId = isAction ? sub : sub.id;
                              const subName = isAction ? sub : sub.name;

                              const isSubSelected = isAction
                                ? isAllSelected
                                : selectedList.includes(subId);

                              const handleClick = () => {
                                const catId = activeCat.id;
                                if (multiSelection) {
                                  if (isAction) {
                                    if (subId === "Select All") {
                                      onSubcategoryToggle(catId, "__SELECT_ALL__");
                                    } else if (subId === "Clear All") {
                                      onSubcategoryToggle(catId, "__CLEAR_ALL__");
                                    }
                                  } else {
                                    onSubcategoryToggle(catId, subId);
                                  }
                                } else {
                                  onSubcategoryToggle(catId, subId)
                                }
                              };

                              return (
                                <button
                                  key={subId}
                                  onClick={handleClick}
                                  className={cn(
                                    "px-4 py-1.5 rounded-full text-xs font-medium text-center border transition whitespace-nowrap m-1",
                                    subName === "Select All" || subName === "Clear All"
                                      ? isSubSelected
                                        ? "bg-black text-white"
                                        : "bg-white text-[#797878] border-[#E5E5E5]"
                                      : isSubSelected
                                        ? "bg-[#905BB5] text-white"
                                        : "bg-white text-[#797878] border-[#E5E5E5]"
                                  )}
                                >
                                  {subName}
                                </button>
                              );
                            });
                          })()}
                        </div>
                      )}
                  </div>
                );
              }
            )}

            {/* Toggle Button */}
            {categoryRows.length > 2 && !showEveryCategories && (
              <button
                onClick={() => setShowAllCategories(!showAllCategories)}
                className="mx-auto mt-6 text-sm text-[#905BB5] flex items-center gap-1"
              >
                {showAllCategories ? "Show Less" : "Show More"}
                <span
                  className={cn("transition-transform", {
                    "rotate-180": showAllCategories,
                  })}
                >
                  ▼
                </span>
              </button>
            )}
          </>
        ) : (
          <div className="text-base text-center py-8">No Tag Found</div>
        )}
      </div>
    </div>
  );
};
