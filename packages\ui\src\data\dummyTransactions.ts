import { Group, Member } from "../components/MemberGroupFilter";
import {
  Category,
  TransactionGroup,
} from "../components/TransactionsTable";

export const dummyTransactions: TransactionGroup[] = [
  {
    date: new Date("2025-03-02T00:00:00.000Z"),
    transactions: [
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a5",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 6883,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 2,
        notes: "testtt Notes ",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61b6",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a6",
        txnMode: "OTHERS",
        txnType: "DEBIT",
        amount: 3386,
        merchantName: "Fbl - Nagpur",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "BY CDM / 3757 / FBL - NAGPUR / CA ROAD \\",
        documentsCount: 1,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61a4",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a7",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 3847,
        merchantName: "MES",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "UPI IN / ************ / abhishekmeshram144 @ oksbi / UPI",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61b2",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a8",
        txnMode: "OTHERS",
        txnType: "DEBIT",
        amount: 2443,
        merchantName: "Pranav Prakash Thote",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "UPI IN / ************ / ********** @ upi / NO REMARKS",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61b0",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a9",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 5316,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61a6",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725aa",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 2706,
        merchantName: "Nilesh.Gurve1234@Oksbi",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "UPI IN/************/nilesh.gurve1234@oksbi/Paym",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61a3",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725ah",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 3772,
        merchantName: "By K P Enterpri",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "CLG / BY K P ENTERPRI / 221386 / 19-01- 21 / UNION BANK",
        documentsCount: 0,
        notes: "New Notes",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61ab",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725ad",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 4030,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 4,
        notes: "Test",
      },
    ],
  },
  {
    date: new Date("2025-03-01T00:00:00.000Z"),
    transactions: [
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a4",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 2648,
        merchantName: "Mr. Sanjay Thakurdas Khattar",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "UPI IN / ************ / ********** @ ybl / Payment from Ph",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61a3",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725ab",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 3772,
        merchantName: "By K P Enterpri",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "CLG / BY K P ENTERPRI / 221386 / 19-01- 21 / UNION BANK",
        documentsCount: 0,
        notes: "New Notes",
      },
      {
        tag: {
          categoryCollection: "",
          categoryId: "67c8025059c3c908d42c61ab",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725ac",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 4030,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: true,
        isExcludedCashflow: true,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 4,
        notes: "Test",
      },
    ],
  },
  {
    date: new Date("2025-02-13T00:00:00.000Z"),
    transactions: [
      {
        tag: {
          categoryCollection: "global",
          categoryId: "67c8025059c3c908d42c61a3",
          subcategoryId: "67c8025059c3c908d42c61c9",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847259b",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 2841,
        merchantName: "Umiya T",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 3,
          year: 2025,
        },
        narration: "NFT / UMIYA T / N359201351036258 / HDFC BANK",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847259c",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 8184,
        merchantName: "By Taj Enterpri",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CLG / BY TAJ ENTERPRI / 876 / 23-03-21 / ICICI BANK",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "67c8025059c3c908d42c61a3",
          subcategoryId: "67c8025059c3c908d42c61c9",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847259d",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 6206,
        merchantName: "Pawansalescorpo",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "NFT / SMEMB2125tdkwu / pawansalescorpo / R N TRADE LIN",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847259e",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 5309,
        merchantName: "By Prajapati El",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CLG / BY PRAJAPATI EL / 38675 / 22-01- 21 / CENTRAL",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847259f",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 1833,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a0",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 9270,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a1",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 7008,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "67c8025059c3c908d42c61a3",
          subcategoryId: "67c8025059c3c908d42c61c9",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a2",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 8695,
        merchantName: "Pawansalescorpo",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "NFT / SMEMB21566ujmn / pawansalescorpo / R N TRADE LIN",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "67c8025059c3c908d42c61a3",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a136984725a3",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 4335,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 0,
        notes: "",
      },
    ],
  },
  {
    date: new Date("2025-02-02T00:00:00.000Z"),
    transactions: [
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847258e",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 6133,
        merchantName: "Varietyenterprise S",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration:
          "IFT IMPS / IFI / ************ / VARIETYENTERPRISE S / Ashriw",
        documentsCount: 0,
        notes: "",
      },
      {
        tag: {
          categoryCollection: "global",
          categoryId: "",
          subcategoryId: "",
        },
        account: {
          accountId: "67dd216b4918a1369847249c",
          linkedAccRef: "be5cc0e6-b20c-4507-9e2e-74224a431a98",
          maskedAccNumber: "XXXXX5917",
          fipId: "BARB0KIMXXX",
          holderName: "Gaurang",
          branch: "ICICI Bank",
          fiType: "DEPOSIT",
          userId: "67dd214a4918a13698472499",
          dataSyncedAt: "**********",
        },
        txnId: "67dd216b4918a1369847258f",
        txnMode: "OTHERS",
        txnType: "CREDIT",
        amount: 6161,
        merchantName: "Cash",
        bankName: "icici",
        isSaved: false,
        isExcludedCashflow: false,
        cashFlowPeriod: {
          month: 2,
          year: 2025,
        },
        narration: "CASH :",
        documentsCount: 0,
        notes: "",
      },
    ],
  },
];

export const Tags: string[] = [
  "All",
  "Tags",
  "Members",
  "Paid to",
  "Notes",
  "Tags",
  "Members",
  "Paid to",
];

export const dummyMembers={
  users: [
    {
        "alternatePhoneNumbers": [],
        "_id": {
            "id": "67dd214a4918a13698472499"
        },
        "phoneNumber": "**********",
        "pan": "**********",
        "name": "Gaurang",
        "address": "Senapati Bapat Road,Pune,Maharashtra,India,411016",
        "dateOfBirth": "1966-06-08",
        "familyId": "67dd214a31ba99290b6277ef",
        "groupId": null,
        "isPrimary": true,
        "lastLoginTime": "0",
        "createdAt": "**********",
        "updatedAt": "**********",
        "nickname": "",
        "email": "",
        "relation": "USER_RELATION_SELF"
    },
    {
      "alternatePhoneNumbers": [],
      "_id": {
          "id": "77dd214a4918a136972499"
      },
      "phoneNumber": "**********",
      "pan": "**********",
      "name": "Jayendra",
      "address": "Senapati Bapat Road,Pune,Maharashtra,India,411016",
      "dateOfBirth": "1966-06-08",
      "familyId": "67dd214a31ba9929b6277eg",
      "groupId": null,
      "isPrimary": true,
      "lastLoginTime": "0",
      "createdAt": "**********",
      "updatedAt": "**********",
      "nickname": "",
      "email": "",
      "relation": "USER_RELATION_SELF"
  }
]

}
export const dummyAccount = {
  accounts: [
    {
      accountId: '67dd216b4918a1369847249c',
      linkedAccRef: 'be5cc0e6-b20c-4507-9e2e-74224a431a98',
      maskedAccNumber: 'XXXXX5917',
      fipId: 'BARB0KIMXXX',
      holderName: 'Gaurang-',
      branch: 'ICICI Bank',
      fiType: 'DEPOSIT',
      userId: '67dd214a4918a13698472499',
      dataSyncedAt: '**********',
    },
    {
      accountId: '67dd216b4918a136984729c',
      linkedAccRef: 'be5cc0e6-b20c-4507-9e2e-74224a431a98',
      maskedAccNumber: 'XXXXX5917',
      fipId: 'BARB0KIMXXX',
      holderName: 'Gaurang-',
      branch: 'Axis Bank',
      fiType: 'DEPOSIT',
      userId: '67dd214a4918a13698472499',
      dataSyncedAt: '**********',
    },
    {
      accountId: '67dd216b4918a1369847249e',
      linkedAccRef: 'be5cc0e6-b20c-4507-9e2e-74224a431a98',
      maskedAccNumber: 'XXXXX1234',
      fipId: 'BARB0KIMXXX',
      holderName: 'Jayendra',
      branch: 'Axis Bank',
      fiType: 'DEPOSIT',
      userId: '77dd214a4918a136972499',
      dataSyncedAt: '**********',
    }
  ]
}

export const dummyGroups = {
  groups: [
    {
      users: [
        {
          alternatePhoneNumbers: [],
          _id: {
            id: "680f54783919ed0ed183ce02"
          },
          phoneNumber: "*********",
          pan: "",
          name: "",
          address: "",
          dateOfBirth: "",
          familyId: "67dd214a31ba99290b6277ef",
          groupId: {
            id: "680f5512bde4dee16c3eda9a"
          },
          isPrimary: false,
          lastLoginTime: "0",
          createdAt: "**********",
          updatedAt: "**********",
          nickname: "Nickname of new member",
          email: "<EMAIL>",
          relation: "USER_RELATION_PARENT"
        },
        {
          alternatePhoneNumbers: [],
          _id: {
            id: "680f54c13919ed0ed183cfdf"
          },
          phoneNumber: "**********",
          pan: "",
          name: "",
          address: "",
          dateOfBirth: "",
          familyId: "67dd214a31ba99290b6277ef",
          groupId: {
            id: "680f5512bde4dee16c3eda9a"
          },
          isPrimary: false,
          lastLoginTime: "0",
          createdAt: "1745835201",
          updatedAt: "1745835282",
          nickname: "Nickname of new member",
          email: "<EMAIL>",
          relation: "USER_RELATION_SIBLING"
        }
      ],
      group: {
        _id: {
          id: "680f5512bde4dee16c3eda9a"
        },
        name: "My Group 1",
        familyId: "67dd214a31ba99290b6277ef",
        createdAt: "1745835282",
        updatedAt: "0"
      }
    },
    {
      users: [
        {
          alternatePhoneNumbers: [],
          _id: {
            id: "67dd214a4918a13698472499"
          },
          phoneNumber: "**********",
          pan: "**********",
          name: "Gaurang",
          address: "Senapati Bapat Road,Pune,Maharashtra,India,411016",
          dateOfBirth: "1966-06-08",
          familyId: "67dd214a31ba99290b6277ef",
          groupId: {
            id: "680f551dbde4dee16c3eda9b"
          },
          isPrimary: true,
          lastLoginTime: "0",
          createdAt: "**********",
          updatedAt: "**********",
          nickname: "",
          email: "",
          relation: "USER_RELATION_SELF"
        }
      ],
      group: {
        _id: {
          id: "680f551dbde4dee16c3eda9b"
        },
        name: "My Group 2",
        familyId: "67dd214a31ba99290b6277ef",
        createdAt: "1745835293",
        updatedAt: "0"
      }
    }
  ]
};
export const creditCategories: Category[] = [
  {
    id: "67c8025059c3c908d42c61a3",
    sfSymbol: "tag_income",
    createdAt: **********,
    sortKey: 2,
    name: "Income",
    iconUrl: "",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bb",
        name: "Interest",
        sortKey: 6,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        createdAt: **********,
        sortKey: 8,
      },
      {
        id: "67c8025059c3c908d42c61bd",
        name: "Gifts Received",
        sortKey: 14,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61be",
        sortKey: 7,
        createdAt: **********,
        name: "Land Sold",
      },
      {
        id: "67c8025059c3c908d42c61bf",
        name: "Business",
        createdAt: **********,
        sortKey: 11,
      },
      {
        id: "67c8025059c3c908d42c61c0",
        createdAt: **********,
        name: "Self Employment",
        sortKey: 10,
      },
      {
        id: "67c8025059c3c908d42c61c1",
        name: "Alimony Received",
        createdAt: **********,
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61c2",
        sortKey: 2,
        createdAt: **********,
        name: "Bonus",
      },
      {
        id: "67c8025059c3c908d42c61c3",
        sortKey: 16,
        name: "Salary",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61c4",
        createdAt: **********,
        sortKey: 5,
        name: "House Sold",
      },
      {
        id: "67c8025059c3c908d42c61c5",
        sortKey: 4,
        name: "Dividend Income",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61c6",
        sortKey: 9,
        createdAt: **********,
        name: "Rental Income",
      },
      {
        id: "67c8025059c3c908d42c61c7",
        sortKey: 13,
        createdAt: **********,
        name: "Tax Refund",
      },
      {
        id: "67c8025059c3c908d42c61c8",
        sortKey: 3,
        createdAt: **********,
        name: "Child Support Received",
      },
      {
        id: "67c8025059c3c908d42c61c9",
        sortKey: 15,
        createdAt: **********,
        name: "Investment Liquidation",
      },
      {
        id: "67c8025059c3c908d42c61ca",
        name: "Vehicle Sold",
        sortKey: 12,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a4",
    createdAt: **********,
    iconUrl: "",
    name: "Misc.",
    sfSymbol: "tag_misc",
    sortKey: 4,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61cb",
        sortKey: 2,
        createdAt: **********,
        name: "Bank Account Verification",
      },
      {
        id: "67c8025059c3c908d42c61cc",
        name: "Return / Refund",
        sortKey: 1,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a5",
    sortKey: 3,
    name: "Self Transfer",
    sfSymbol: "tag_selfTransfer",
    iconUrl: "",
    createdAt: **********,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        name: "Others",
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61cd",
        name: "Saving",
        sortKey: 2,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61ce",
        name: "Monthly Expense",
        sortKey: 3,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a6",
    iconUrl: "",
    createdAt: **********,
    sfSymbol: "tag_debt",
    name: "Debt",
    sortKey: 5,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 1,
        name: "Others",
        createdAt: **********,
      },
    ],
  },
];

export const debitCategories: Category[] = [
  {
    id: "67c8025059c3c908d42c61a4",
    sfSymbol: "tag_misc",
    iconUrl: "",
    sortKey: 16,
    name: "Misc.",
    createdAt: **********,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61cb",
        createdAt: **********,
        name: "Bank Account Verification",
        sortKey: 5,
      },
      {
        id: "67c8025059c3c908d42c61d0",
        name: "Bank Charge",
        sortKey: 1,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61d1",
        createdAt: **********,
        sortKey: 7,
        name: "Registration Fee",
      },
      {
        id: "67c8025059c3c908d42c61d2",
        createdAt: **********,
        name: "Tip",
        sortKey: 2,
      },
      {
        id: "67c8025059c3c908d42c61d3",
        name: "Shopping",
        createdAt: **********,
        sortKey: 6,
      },
      {
        id: "67c8025059c3c908d42c61d4",
        name: "Lent",
        createdAt: **********,
        sortKey: 3,
      },
      {
        id: "67c8025059c3c908d42c61d5",
        name: "Cash Withdrawal",
        sortKey: 4,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a5",
    iconUrl: "",
    sortKey: 20,
    createdAt: **********,
    sfSymbol: "tag_selfTransfer",
    name: "Self Transfer",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        sortKey: 2,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61cd",
        createdAt: **********,
        sortKey: 3,
        name: "Saving",
      },
      {
        id: "67c8025059c3c908d42c61ce",
        sortKey: 1,
        createdAt: **********,
        name: "Monthly Expense",
      },
      {
        id: "67c8025059c3c908d42c621d",
        name: "Wallet Top-Up",
        sortKey: 4,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a6",
    sfSymbol: "tag_debt",
    createdAt: **********,
    sortKey: 4,
    name: "Debt",
    iconUrl: "",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        createdAt: **********,
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c621e",
        name: "Personal Loan",
        createdAt: **********,
        sortKey: 4,
      },
      {
        id: "67c8025059c3c908d42c621f",
        sortKey: 5,
        name: "Student Loan",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6220",
        name: "Vehicle Loan",
        createdAt: **********,
        sortKey: 6,
      },
      {
        id: "67c8025059c3c908d42c6221",
        sortKey: 2,
        name: "Electronics Loan",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6222",
        name: "Home Loan",
        sortKey: 3,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a7",
    createdAt: **********,
    sfSymbol: "tag_subscription",
    iconUrl: "",
    name: "Subscription",
    sortKey: 22,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 2,
        createdAt: **********,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c61cf",
        createdAt: **********,
        name: "Apps & Software",
        sortKey: 1,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a8",
    sortKey: 15,
    name: "Medical",
    createdAt: **********,
    sfSymbol: "tag_medical",
    iconUrl: "",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 4,
        createdAt: **********,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c61d6",
        name: "Hospital",
        createdAt: **********,
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61d7",
        sortKey: 2,
        name: "Lab Test",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61d8",
        sortKey: 3,
        name: "Pharmacy",
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61a9",
    name: "Child",
    iconUrl: "",
    sortKey: 6,
    sfSymbol: "tag_child",
    createdAt: **********,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 2,
        createdAt: **********,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c61d9",
        createdAt: **********,
        name: "Baby Care",
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61da",
        createdAt: **********,
        sortKey: 3,
        name: "Study Expenses",
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61aa",
    name: "Clothing",
    createdAt: **********,
    sfSymbol: "tag_clothing",
    sortKey: 7,
    iconUrl: "",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        sortKey: 4,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61db",
        createdAt: **********,
        sortKey: 5,
        name: "Watch",
      },
      {
        id: "67c8025059c3c908d42c61dc",
        sortKey: 1,
        createdAt: **********,
        name: "Footwear",
      },
      {
        id: "67c8025059c3c908d42c61dd",
        name: "Eyewear",
        createdAt: **********,
        sortKey: 2,
      },
      {
        id: "67c8025059c3c908d42c61de",
        sortKey: 3,
        createdAt: **********,
        name: "Jewelry",
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61ab",
    iconUrl: "",
    sortKey: 3,
    createdAt: **********,
    sfSymbol: "tag_utilityBills",
    name: "Recurrent Payment",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        name: "Others",
        sortKey: 5,
      },
      {
        id: "67c8025059c3c908d42c61df",
        sortKey: 1,
        name: "Salary Paid",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61e0",
        name: "Guard",
        sortKey: 9,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61e1",
        name: "Asset Maintenance",
        createdAt: **********,
        sortKey: 4,
      },
      {
        id: "67c8025059c3c908d42c61e2",
        createdAt: **********,
        name: "News Paper",
        sortKey: 10,
      },
      {
        id: "67c8025059c3c908d42c61e3",
        sortKey: 6,
        createdAt: **********,
        name: "Rent",
      },
      {
        id: "67c8025059c3c908d42c61e4",
        sortKey: 2,
        createdAt: **********,
        name: "Recharge",
      },
      {
        id: "67c8025059c3c908d42c61e5",
        name: "Credit Card Bill",
        createdAt: **********,
        sortKey: 7,
      },
      {
        id: "67c8025059c3c908d42c61e6",
        sortKey: 8,
        createdAt: **********,
        name: "House Help",
      },
      {
        id: "67c8025059c3c908d42c61e7",
        name: "Electricity Bill",
        sortKey: 3,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61ac",
    name: "Body Care",
    iconUrl: "",
    sfSymbol: "tag_bodyCare",
    createdAt: **********,
    sortKey: 5,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 2,
        name: "Others",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61e8",
        name: "Beauty",
        createdAt: **********,
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61e9",
        createdAt: **********,
        name: "Vice",
        sortKey: 4,
      },
      {
        id: "67c8025059c3c908d42c61ea",
        name: "Therapy",
        sortKey: 3,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61ad",
    iconUrl: "",
    name: "Tax",
    sfSymbol: "tag_tax",
    sortKey: 23,
    createdAt: **********,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 4,
        name: "Others",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61eb",
        name: "Property Tax",
        createdAt: **********,
        sortKey: 5,
      },
      {
        id: "67c8025059c3c908d42c61ec",
        name: "Income Tax",
        sortKey: 3,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61ed",
        createdAt: **********,
        name: "Back Tax",
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61ee",
        sortKey: 2,
        name: "GST",
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61ae",
    sortKey: 9,
    name: "Food & Drink",
    createdAt: **********,
    sfSymbol: "tag_foodDrink",
    iconUrl: "",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        name: "Others",
        sortKey: 7,
      },
      {
        id: "67c8025059c3c908d42c61ef",
        createdAt: **********,
        sortKey: 8,
        name: "Tiffin",
      },
      {
        id: "67c8025059c3c908d42c61f0",
        createdAt: **********,
        sortKey: 2,
        name: "Breakfast",
      },
      {
        id: "67c8025059c3c908d42c61f1",
        name: "Dinner",
        createdAt: **********,
        sortKey: 4,
      },
      {
        id: "67c8025059c3c908d42c61f2",
        sortKey: 3,
        name: "Drink",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61f3",
        sortKey: 1,
        name: "Food",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61f4",
        name: "Lunch",
        sortKey: 5,
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61af",
    sfSymbol: "tag_petCare",
    createdAt: **********,
    iconUrl: "",
    name: "Pet Care",
    sortKey: 19,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        name: "Others",
        sortKey: 3,
      },
      {
        id: "67c8025059c3c908d42c61f5",
        createdAt: **********,
        sortKey: 1,
        name: "Pet Accessory",
      },
      {
        id: "67c8025059c3c908d42c61f6",
        name: "Pet Health Care",
        createdAt: **********,
        sortKey: 2,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b0",
    sortKey: 2,
    createdAt: **********,
    sfSymbol: "tag_support",
    name: "Support",
    iconUrl: "",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 4,
        name: "Others",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61f7",
        createdAt: **********,
        sortKey: 3,
        name: "Donation",
      },
      {
        id: "67c8025059c3c908d42c61f8",
        createdAt: **********,
        sortKey: 2,
        name: "Family",
      },
      {
        id: "67c8025059c3c908d42c61f9",
        sortKey: 1,
        name: "Alimony",
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b1",
    sfSymbol: "tag_household",
    iconUrl: "",
    createdAt: **********,
    name: "Household",
    sortKey: 12,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        sortKey: 5,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c61fa",
        createdAt: **********,
        name: "Home Appliance",
        sortKey: 2,
      },
      {
        id: "67c8025059c3c908d42c61fb",
        createdAt: **********,
        sortKey: 4,
        name: "Interior",
      },
      {
        id: "67c8025059c3c908d42c61fc",
        name: "Cleaning Tool / Product",
        createdAt: **********,
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c61fd",
        sortKey: 6,
        name: "Grocery",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c61fe",
        name: "Kitchen Utility",
        createdAt: **********,
        sortKey: 3,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b2",
    sortKey: 10,
    name: "Fun",
    sfSymbol: "tag_fun",
    iconUrl: "",
    createdAt: **********,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        sortKey: 3,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c61ff",
        sortKey: 4,
        name: "House Party",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6200",
        sortKey: 6,
        createdAt: **********,
        name: "Show",
      },
      {
        id: "67c8025059c3c908d42c6201",
        name: "Concert",
        createdAt: **********,
        sortKey: 2,
      },
      {
        id: "67c8025059c3c908d42c6202",
        createdAt: **********,
        name: "Celebration",
        sortKey: 7,
      },
      {
        id: "67c8025059c3c908d42c6203",
        sortKey: 1,
        createdAt: **********,
        name: "Game",
      },
      {
        id: "67c8025059c3c908d42c6204",
        name: "Movie",
        createdAt: **********,
        sortKey: 5,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b3",
    sfSymbol: "tag_fitness",
    iconUrl: "",
    name: "Fitness",
    sortKey: 8,
    createdAt: **********,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        name: "Others",
        sortKey: 1,
      },
      {
        id: "67c8025059c3c908d42c6205",
        sortKey: 2,
        name: "Gym",
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b4",
    createdAt: **********,
    name: "Travel or Transportation",
    sortKey: 24,
    iconUrl: "",
    sfSymbol: "tag_travelTransportation",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        sortKey: 1,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6206",
        name: "Fuel",
        sortKey: 6,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6207",
        createdAt: **********,
        name: "Flight",
        sortKey: 5,
      },
      {
        id: "67c8025059c3c908d42c6208",
        sortKey: 16,
        createdAt: **********,
        name: "Challan",
      },
      {
        id: "67c8025059c3c908d42c6209",
        sortKey: 10,
        name: "Friendly Trip",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c620a",
        name: "Parking",
        sortKey: 7,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c620b",
        createdAt: **********,
        name: "Souvenir",
        sortKey: 12,
      },
      {
        id: "67c8025059c3c908d42c620c",
        name: "Hometown Visit",
        createdAt: **********,
        sortKey: 11,
      },
      {
        id: "67c8025059c3c908d42c620d",
        name: "Activity",
        sortKey: 8,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c620e",
        sortKey: 9,
        name: "Stay",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c620f",
        createdAt: **********,
        sortKey: 17,
        name: "Baggage Fee",
      },
      {
        id: "67c8025059c3c908d42c6210",
        sortKey: 4,
        name: "FASTag / Toll",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6211",
        sortKey: 15,
        createdAt: **********,
        name: "Wedding Trip",
      },
      {
        id: "67c8025059c3c908d42c6212",
        name: "Public Transport",
        sortKey: 3,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6213",
        sortKey: 2,
        name: "Ride Booking",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6214",
        sortKey: 14,
        createdAt: **********,
        name: "Visa Fee",
      },
      {
        id: "67c8025059c3c908d42c6215",
        createdAt: **********,
        sortKey: 13,
        name: "Vacation",
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b5",
    sfSymbol: "tag_insurance",
    name: "Insurance",
    createdAt: **********,
    iconUrl: "",
    sortKey: 13,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        sortKey: 3,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6216",
        sortKey: 2,
        createdAt: **********,
        name: "Life Insurance",
      },
      {
        id: "67c8025059c3c908d42c6217",
        sortKey: 1,
        createdAt: **********,
        name: "Health Insurance",
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b6",
    sfSymbol: "tag_gifts",
    createdAt: **********,
    sortKey: 11,
    iconUrl: "",
    name: "Gifts",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        sortKey: 1,
        name: "Others",
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b7",
    sortKey: 21,
    createdAt: **********,
    iconUrl: "",
    name: "Service",
    sfSymbol: "tag_service",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        name: "Others",
        sortKey: 1,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6218",
        sortKey: 3,
        name: "Courier",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6219",
        sortKey: 2,
        name: "Professional Service",
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c621a",
        sortKey: 4,
        createdAt: **********,
        name: "Xerox",
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b8",
    createdAt: **********,
    sfSymbol: "tag_personalAsset",
    name: "Personal Asset",
    iconUrl: "",
    sortKey: 17,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        name: "Others",
        sortKey: 3,
      },
      {
        id: "67c8025059c3c908d42c621b",
        sortKey: 1,
        createdAt: **********,
        name: "Electronics",
      },
      {
        id: "67c8025059c3c908d42c621c",
        sortKey: 2,
        name: "Vehicle",
        createdAt: **********,
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61b9",
    sortKey: 18,
    createdAt: **********,
    iconUrl: "",
    name: "Personal Growth",
    sfSymbol: "tag_personalGrowth",
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        sortKey: 4,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c6223",
        sortKey: 1,
        createdAt: **********,
        name: "Book",
      },
      {
        id: "67c8025059c3c908d42c6224",
        name: "Personal Coach",
        createdAt: **********,
        sortKey: 5,
      },
      {
        id: "67c8025059c3c908d42c6225",
        sortKey: 3,
        createdAt: **********,
        name: "Course",
      },
      {
        id: "67c8025059c3c908d42c6226",
        sortKey: 2,
        createdAt: **********,
        name: "Conference",
      },
    ],
  },
  {
    id: "67c8025059c3c908d42c61ba",
    iconUrl: "",
    sfSymbol: "tag_investment",
    createdAt: **********,
    name: "Investment",
    sortKey: 14,
    cashflowGroup: 2,
    subCategories: [
      {
        id: "67c8025059c3c908d42c61bc",
        createdAt: **********,
        sortKey: 1,
        name: "Others",
      },
      {
        id: "67c8025059c3c908d42c6227",
        name: "National Pension Scheme (NPS)",
        sortKey: 6,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c6228",
        sortKey: 3,
        createdAt: **********,
        name: "Emergency Fund",
      },
      {
        id: "67c8025059c3c908d42c6229",
        createdAt: **********,
        sortKey: 10,
        name: "Bonds",
      },
      {
        id: "67c8025059c3c908d42c622a",
        name: "Mutual Fund / SIP / NFO",
        sortKey: 5,
        createdAt: **********,
      },
      {
        id: "67c8025059c3c908d42c622b",
        sortKey: 4,
        createdAt: **********,
        name: "Fixed Deposit",
      },
      {
        id: "67c8025059c3c908d42c622c",
        name: "Public Provident Fund (PPF)",
        createdAt: **********,
        sortKey: 7,
      },
      {
        id: "67c8025059c3c908d42c622d",
        sortKey: 9,
        createdAt: **********,
        name: "Stocks / IPO",
      },
      {
        id: "67c8025059c3c908d42c622e",
        name: "Crypto",
        createdAt: **********,
        sortKey: 2,
      },
      {
        id: "67c8025059c3c908d42c622f",
        name: "Recurring Deposit",
        createdAt: **********,
        sortKey: 8,
      },
    ],
  },
];
