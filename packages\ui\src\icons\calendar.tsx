interface CalendarSvgIconProps {
  className?: string;
}

export const CalendarSvgIcon: React.FC<CalendarSvgIconProps> = ({
  className = "text-[#EBE0F4]",
}) => {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g id="calendar" clipPath="url(#clip0_7926_6326)">
        <path
          id="Vector"
          d="M13.6496 3.48828H4.31624C3.57986 3.48828 2.98291 4.08523 2.98291 4.82161V14.1549C2.98291 14.8913 3.57986 15.4883 4.31624 15.4883H13.6496C14.386 15.4883 14.9829 14.8913 14.9829 14.1549V4.82161C14.9829 4.08523 14.386 3.48828 13.6496 3.48828Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          id="Vector_2"
          d="M11.6489 2.15625V4.82292"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          id="Vector_3"
          d="M6.31689 2.15625V4.82292"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          id="Vector_4"
          d="M2.98291 7.48828H14.9829"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7926_6326">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0.98291 0.822266)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
