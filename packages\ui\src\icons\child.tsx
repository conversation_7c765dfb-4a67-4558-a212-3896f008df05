import * as React from "react";
import type { SVGProps } from "react";
const SvgChild = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
    fill="currentColor"
    viewBox="0 0 31 30"
    {...props}
  >
    <path d="M28.626 22.608a2.52 2.52 0 0 0-2.158-.055V13.61q.375.41.7.864a1.62 1.62 0 0 0 1.62.635c.61-.124 1.104-.596 1.28-1.224a1.76 1.76 0 0 0-.266-1.493c-1.886-2.643-5.601-5.755-12.729-6.156.032-.266.153-.51.34-.688 2.717-2.721-.794-6.995-3.747-4.594a3.1 3.1 0 0 0-1.03 2.282 3.13 3.13 0 0 0 .957 2.317c.184.178.303.42.335.683C6.8 6.637 3.079 9.75 1.197 12.393v-.002a1.76 1.76 0 0 0-.266 1.493c.179.63.674 1.1 1.284 1.223a1.62 1.62 0 0 0 1.621-.64q.323-.45.695-.858v8.933a2.55 2.55 0 0 0-2.466.273c-.729.512-1.173 1.37-1.186 2.294.073 2.495 3.6 4.257 4.175 4.4.585-.148 4.107-1.9 4.18-4.45-.01-1.05-.59-2.003-1.494-2.45a2.53 2.53 0 0 0-2.164-.056v-9.925c1.766-1.437 4.693-2.836 9.4-2.94v7.77a1.32 1.32 0 0 0-.788.887l-.523 1.697a.326.326 0 0 1-.313.242H11.66c-.598.002-1.126.406-1.31 1.004a1.5 1.5 0 0 0 .5 1.623l1.369 1.052c.116.09.166.248.12.391l-.523 1.697a1.5 1.5 0 0 0 .502 1.625 1.32 1.32 0 0 0 1.62 0l1.369-1.052a.324.324 0 0 1 .392 0l1.369 1.052c.485.36 1.131.355 1.61-.013a1.5 1.5 0 0 0 .506-1.612l-.522-1.697a.37.37 0 0 1 .12-.39l1.374-1.053c.47-.376.664-1.024.48-1.616-.18-.592-.7-.998-1.29-1.01h-1.698a.33.33 0 0 1-.314-.243l-.522-1.697a1.32 1.32 0 0 0-.79-.886V9.687c4.709.1 7.635 1.498 9.401 2.936v9.92a2.55 2.55 0 0 0-2.469.27 2.85 2.85 0 0 0-1.189 2.296c.074 2.495 3.6 4.257 4.18 4.4.58-.143 4.103-1.9 4.176-4.45-.01-1.052-.59-2.005-1.495-2.45" />
  </svg>
);
export default SvgChild;
